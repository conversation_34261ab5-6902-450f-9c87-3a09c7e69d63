"""
ARIA Agent Dashboard - Streamlit Application
Standalone dashboard for claim review and management
"""

import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta

from aria_system.config import Settings
from aria_system.database.supabase_client import SupabaseClient

# Configure Streamlit page
st.set_page_config(
    page_title="ARIA Agent Portal",
    page_icon="👥",
    layout="wide",
    initial_sidebar_state="expanded"
)

def main():
    """Main dashboard application"""
    
    # Custom CSS
    st.markdown("""
    <style>
    .claim-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 16px;
        margin: 8px 0;
        background: white;
    }
    .priority-urgent { border-left: 4px solid #dc3545; }
    .priority-high { border-left: 4px solid #fd7e14; }
    .priority-normal { border-left: 4px solid #28a745; }
    .priority-low { border-left: 4px solid #6c757d; }
    
    .confidence-high { color: #28a745; font-weight: bold; }
    .confidence-medium { color: #fd7e14; font-weight: bold; }
    .confidence-low { color: #dc3545; font-weight: bold; }
    </style>
    """, unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.title("👥 ARIA Agent Portal")
        st.success("System Status: ✅ Online")
        st.info("API: http://localhost:8001")
        
        # Navigation
        page = st.selectbox(
            "Navigate to:",
            ["Dashboard", "Claims Queue", "System Status", "Configuration"]
        )
    
    # Main content
    if page == "Dashboard":
        render_dashboard()
    elif page == "Claims Queue":
        render_claims_queue()
    elif page == "System Status":
        render_system_status()
    elif page == "Configuration":
        render_configuration()

def render_dashboard():
    """Render main dashboard"""
    st.title("📊 ARIA Claims Dashboard")
    
    # Metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Claims", "0", "System starting")
    with col2:
        st.metric("Pending Review", "0", "Ready for claims")
    with col3:
        st.metric("Processed Today", "0", "System ready")
    with col4:
        st.metric("System Health", "100%", "All services online")
    
    # Quick actions
    st.subheader("🚀 Quick Actions")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📧 Test Email Processing", use_container_width=True):
            st.success("Email monitoring is active!")
    
    with col2:
        if st.button("🤖 Test AI Analysis", use_container_width=True):
            st.success("AI analysis ready!")
    
    with col3:
        if st.button("📱 Test Notifications", use_container_width=True):
            st.success("HumanLayer notifications ready!")
    
    # System overview
    st.subheader("🔧 System Overview")
    
    status_data = {
        "Service": ["Email Monitor", "AI Analysis", "Zendesk", "HumanLayer", "Database"],
        "Status": ["🟢 Running", "🟢 Ready", "🟢 Connected", "🟢 Active", "🟡 Schema Pending"],
        "Description": [
            "Monitoring <EMAIL> for claims",
            "OpenAI GPT-4 + Zurich OCR ready",
            "Connected to d3v-rozieai5417.zendesk.com",
            "Human-in-the-loop workflows active",
            "Supabase connected, schema needs setup"
        ]
    }
    
    df = pd.DataFrame(status_data)
    st.dataframe(df, use_container_width=True, hide_index=True)

def render_claims_queue():
    """Render claims queue"""
    st.title("📋 Claims Processing Queue")
    
    st.info("🚀 System is ready to receive claims!")
    
    st.subheader("📧 How to Submit a Test Claim")
    st.write("""
    1. **Send an email** to: `<EMAIL>`
    2. **Subject**: "Auto accident claim - Need assistance"
    3. **Attach documents**: Photos, police reports, etc.
    4. **Watch the magic happen**:
       - ✅ Instant acknowledgment
       - 🎫 Zendesk ticket creation
       - 🤖 AI analysis
       - 👥 Human review assignment
       - 📱 Multi-channel notifications
    """)
    
    st.subheader("📊 Processing Workflow")
    
    workflow_steps = [
        "📧 Email Received",
        "🔍 Document Analysis (OCR)",
        "🤖 AI Risk Assessment", 
        "👥 Human Agent Assignment",
        "✅ Human Decision Required",
        "📬 Customer Notification"
    ]
    
    for i, step in enumerate(workflow_steps):
        st.write(f"{i+1}. {step}")

def render_system_status():
    """Render system status"""
    st.title("🔧 System Status")
    
    try:
        settings = Settings()
        
        st.subheader("✅ Configuration Status")
        
        config_status = {
            "Component": [
                "Supabase Database",
                "OpenAI API", 
                "HumanLayer",
                "Claims Email",
                "Zendesk",
                "Slack Channel"
            ],
            "Status": [
                "✅ Connected",
                "✅ Configured",
                "✅ Active",
                "✅ Monitoring",
                "✅ Integrated",
                "✅ Ready"
            ],
            "Details": [
                f"URL: {settings.database.supabase_url}",
                f"Model: {settings.ai.openai_model}",
                f"Run ID: {settings.ai.humanlayer_run_id}",
                f"Email: {settings.email.claims_email}",
                f"Subdomain: {settings.zendesk.subdomain}",
                f"Channel: {settings.slack.claims_channel}"
            ]
        }
        
        df = pd.DataFrame(config_status)
        st.dataframe(df, use_container_width=True, hide_index=True)
        
        st.subheader("🔒 Security Features")
        st.success("✅ NO AUTO-APPROVAL: All claims require human review")
        st.success("✅ HumanLayer Integration: Human oversight for all notifications")
        st.success("✅ Complete Audit Trail: All actions logged")
        st.success("✅ SMS/WhatsApp Disabled: Email + Slack only")
        
    except Exception as e:
        st.error(f"Configuration error: {e}")

def render_configuration():
    """Render configuration page"""
    st.title("⚙️ System Configuration")
    
    st.subheader("📋 Next Steps")
    
    st.warning("🗄️ Database Schema Setup Required")
    st.write("""
    **To complete the setup:**
    
    1. Go to: https://tlduggpohclrgxbvuzhd.supabase.co
    2. Navigate to: **SQL Editor**
    3. Copy the schema from: `database/supabase_schema.sql`
    4. Paste and click **"Run"**
    
    This will create all the necessary tables for claims processing.
    """)
    
    if st.button("📋 Show Schema File Location"):
        st.code("aria_system/database/supabase_schema.sql")
    
    st.subheader("🚀 Start Processing Claims")
    st.write("""
    Once the schema is applied:
    
    1. **Send test email** to: <EMAIL>
    2. **Watch real-time processing** in this dashboard
    3. **Review claims** in the Claims Queue
    4. **Make decisions** with human oversight
    """)

if __name__ == "__main__":
    main()
