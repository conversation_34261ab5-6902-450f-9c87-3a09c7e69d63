#!/usr/bin/env python3
"""
ARIA Demo Runner
Quick start script for running ARIA system demo.
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path

def print_banner():
    """Print ARIA banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║    🤖 ARIA - Autonomous Risk Intelligence Agent              ║
    ║                                                              ║
    ║    Production-Quality Insurance Claims Processing            ║
    ║    with AI and Human-in-the-Loop                            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Check if required dependencies are installed."""
    print("🔍 Checking requirements...")
    
    try:
        import aria_system
        print("✅ ARIA system package found")
    except ImportError:
        print("❌ ARIA system not installed. Run: pip install -e .")
        return False
    
    try:
        import streamlit
        print("✅ Streamlit found")
    except ImportError:
        print("❌ Streamlit not installed. Run: pip install streamlit")
        return False
    
    # Check for .env file
    env_file = Path(".env")
    if env_file.exists():
        print("✅ Environment file found")
    else:
        print("⚠️  No .env file found. Copy .env.example to .env and configure")
        return False
    
    return True

def run_initialization():
    """Initialize the ARIA system."""
    print("\n🚀 Initializing ARIA system...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "aria_system.cli", "init"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ ARIA system initialized successfully")
            return True
        else:
            print(f"❌ Initialization failed: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        return False

def start_dashboard():
    """Start the Streamlit dashboard."""
    print("\n📊 Starting ARIA dashboard...")
    
    try:
        dashboard_path = Path("aria_system/dashboard/main.py")
        
        subprocess.Popen([
            sys.executable, "-m", "streamlit", "run",
            str(dashboard_path),
            "--server.port", "8501",
            "--server.address", "localhost"
        ])
        
        print("✅ Dashboard starting at http://localhost:8501")
        return True
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        return False

def start_system():
    """Start the ARIA system."""
    print("\n🔄 Starting ARIA system...")
    
    try:
        # Start system in background
        subprocess.Popen([
            sys.executable, "-m", "aria_system.cli", "start",
            "--no-dashboard"  # Dashboard started separately
        ])
        
        print("✅ ARIA system started")
        return True
    except Exception as e:
        print(f"❌ Error starting system: {e}")
        return False

def show_demo_instructions():
    """Show demo instructions."""
    instructions = """
    
    🎯 DEMO INSTRUCTIONS
    ═══════════════════════════════════════════════════════════════
    
    1. 📊 DASHBOARD ACCESS
       • Open: http://localhost:8501
       • Navigate through different sections
       • Monitor real-time metrics
    
    2. 📧 EMAIL TESTING
       • Use the "Email Claims Processing" section
       • Click "Check for New Emails" to simulate processing
       • Watch the processing steps in real-time
    
    3. 🤝 HUMAN TOOLS TESTING
       • Go to "Human Expert Tools" section
       • Select different expert types
       • Test human-as-tool interactions
    
    4. 🤖 AI PLAYGROUND
       • Use "AI Agent Playground" to test claim analysis
       • Enter claim descriptions and see AI responses
       • Observe confidence scores and decisions
    
    5. 📈 ANALYTICS
       • View "Analytics & Insights" for performance metrics
       • See processing time trends
       • Monitor expert utilization
    
    6. 🔍 CLAIM TRACKING
       • Test individual claim tracking
       • View processing timelines
       • See status updates
    
    ═══════════════════════════════════════════════════════════════
    
    💡 DEMO HIGHLIGHTS:
    
    • Real-time processing simulation
    • Multi-channel human expert integration
    • AI-powered claim analysis
    • Complete audit trails
    • Interactive dashboards
    
    🎬 For best demo experience:
    1. Start with the Real-Time Dashboard
    2. Process a few test emails
    3. Show human expert interactions
    4. Demonstrate AI analysis capabilities
    5. Review analytics and insights
    
    ═══════════════════════════════════════════════════════════════
    """
    print(instructions)

def main():
    """Main demo runner."""
    print_banner()
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed. Please install missing dependencies.")
        sys.exit(1)
    
    # Initialize system
    if not run_initialization():
        print("\n❌ System initialization failed.")
        sys.exit(1)
    
    # Start dashboard
    if not start_dashboard():
        print("\n❌ Dashboard startup failed.")
        sys.exit(1)
    
    # Start system
    if not start_system():
        print("\n❌ System startup failed.")
        sys.exit(1)
    
    # Wait a moment for services to start
    print("\n⏳ Waiting for services to start...")
    time.sleep(5)
    
    # Show demo instructions
    show_demo_instructions()
    
    print("\n🎉 ARIA Demo is ready!")
    print("📊 Dashboard: http://localhost:8501")
    print("\nPress Ctrl+C to stop the demo")
    
    try:
        # Keep the script running
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Stopping ARIA demo...")
        print("Thank you for trying ARIA!")

if __name__ == "__main__":
    main()
