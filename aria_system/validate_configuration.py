"""
ARIA Configuration Validation Script
Validates all configuration and identifies missing credentials
"""

import os
from pathlib import Path
from aria_system.config import Settings

def validate_configuration():
    """Validate ARIA system configuration"""
    print("🔍 ARIA System Configuration Validation")
    print("=" * 60)
    
    try:
        settings = Settings()
        
        # Track validation results
        required_items = []
        optional_items = []
        configured_items = []
        
        print("\n✅ CONFIGURED SERVICES:")
        print("-" * 30)
        
        # Database (Supabase) - REQUIRED
        if settings.database.supabase_url and "tlduggpohclrgxbvuzhd" in settings.database.supabase_url:
            configured_items.append("✅ Supabase Database")
            print(f"  Database: {settings.database.supabase_url}")
            print(f"  Anon Key: {settings.database.supabase_anon_key[:20]}...")
        else:
            required_items.append("❌ Supabase Database configuration")
        
        # OpenAI - REQUIRED for AI analysis
        if settings.ai.openai_api_key and not settings.ai.openai_api_key.startswith("demo"):
            configured_items.append("✅ OpenAI API")
            print(f"  OpenAI: {settings.ai.openai_api_key[:10]}...")
        else:
            required_items.append("❌ OpenAI API Key")
        
        # HumanLayer - REQUIRED for notifications
        if settings.ai.humanlayer_api_key and settings.ai.humanlayer_api_key.startswith("hl-"):
            configured_items.append("✅ HumanLayer")
            print(f"  HumanLayer: {settings.ai.humanlayer_api_key[:15]}...")
        else:
            required_items.append("❌ HumanLayer API Key")
        
        # Email - REQUIRED for claims processing
        if settings.email.claims_email and "@" in settings.email.claims_email:
            configured_items.append("✅ Claims Email")
            print(f"  Claims Email: {settings.email.claims_email}")
        else:
            required_items.append("❌ Claims Email configuration")
        
        # Zendesk - CONFIGURED
        if settings.zendesk.subdomain and settings.zendesk.api_token:
            configured_items.append("✅ Zendesk Integration")
            print(f"  Zendesk: {settings.zendesk.subdomain}.zendesk.com")
        else:
            optional_items.append("⚠️  Zendesk Integration")
        
        # Slack - CONFIGURED
        if settings.slack.claims_channel:
            configured_items.append("✅ Slack Channel")
            print(f"  Slack Channel: {settings.slack.claims_channel}")
        else:
            optional_items.append("⚠️  Slack Channel")
        
        print("\n🚫 AUTO-APPROVAL VALIDATION:")
        print("-" * 30)
        
        # Validate NO auto-approval
        auto_approval_disabled = not getattr(settings.app, 'enable_auto_approval', True)
        human_review_threshold = getattr(settings.app, 'human_review_threshold', 10000)
        
        if auto_approval_disabled and human_review_threshold == 0:
            print("  ✅ Auto-approval is DISABLED")
            print("  ✅ All claims require human review")
            print("  ✅ Human review threshold: 0 (all claims)")
        else:
            print("  ❌ WARNING: Auto-approval may be enabled!")
            print(f"  ❌ Human review threshold: {human_review_threshold}")
        
        print("\n📧 NOTIFICATION CHANNELS:")
        print("-" * 30)
        
        # Check notification settings
        email_enabled = getattr(settings.notifications, 'enable_email_notifications', True)
        sms_enabled = getattr(settings.notifications, 'enable_sms_notifications', True)
        whatsapp_enabled = getattr(settings.notifications, 'enable_whatsapp_notifications', True)
        slack_enabled = getattr(settings.notifications, 'enable_slack_notifications', True)
        
        print(f"  Email: {'✅ Enabled' if email_enabled else '❌ Disabled'}")
        print(f"  SMS: {'❌ Disabled (as requested)' if not sms_enabled else '✅ Enabled'}")
        print(f"  WhatsApp: {'❌ Disabled (as requested)' if not whatsapp_enabled else '✅ Enabled'}")
        print(f"  Slack: {'✅ Enabled' if slack_enabled else '❌ Disabled'}")
        
        print("\n📋 MISSING CREDENTIALS:")
        print("-" * 30)
        
        if required_items:
            for item in required_items:
                print(f"  {item}")
        else:
            print("  🎉 All required credentials are configured!")
        
        if optional_items:
            print("\n⚠️  OPTIONAL ITEMS:")
            print("-" * 30)
            for item in optional_items:
                print(f"  {item}")
        
        print("\n📊 SUMMARY:")
        print("-" * 30)
        print(f"  Configured: {len(configured_items)}")
        print(f"  Missing Required: {len(required_items)}")
        print(f"  Optional: {len(optional_items)}")
        
        if len(required_items) == 0:
            print("\n🎉 SYSTEM READY!")
            print("All required credentials are configured.")
            print("You can start the system with:")
            print("  python3 main.py serve")
        else:
            print("\n⚠️  SYSTEM NOT READY")
            print("Please configure the missing required items above.")
        
        print("\n🔒 SECURITY VALIDATION:")
        print("-" * 30)
        print("  ✅ No auto-approval functionality")
        print("  ✅ All claims require human oversight")
        print("  ✅ HumanLayer integration for notifications")
        print("  ✅ SMS/WhatsApp disabled as requested")
        
        return len(required_items) == 0
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False


def check_database_schema():
    """Check if database schema needs to be applied"""
    print("\n🗄️  DATABASE SCHEMA CHECK:")
    print("-" * 30)
    
    schema_file = Path("database/supabase_schema.sql")
    if schema_file.exists():
        print(f"  ✅ Schema file found: {schema_file}")
        print("  📝 Next step: Apply schema in Supabase SQL editor")
        print(f"     1. Go to: https://tlduggpohclrgxbvuzhd.supabase.co")
        print("     2. Navigate to SQL Editor")
        print("     3. Copy and paste the schema file contents")
        print("     4. Click 'Run' to execute")
    else:
        print("  ❌ Schema file not found")


def main():
    """Main validation function"""
    is_ready = validate_configuration()
    check_database_schema()
    
    print("\n" + "=" * 60)
    if is_ready:
        print("🚀 ARIA SYSTEM IS READY TO START!")
    else:
        print("⚠️  PLEASE CONFIGURE MISSING ITEMS BEFORE STARTING")
    print("=" * 60)


if __name__ == "__main__":
    main()
