-- ARIA Claims Processing System - Supabase Database Schema
-- Complete schema for real-time claims processing with audit trails

-- =============================================================================
-- EXTENSIONS AND SETUP
-- =============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =============================================================================
-- ENUMS AND TYPES
-- =============================================================================

-- Claim status enum
CREATE TYPE claim_status AS ENUM (
    'received',
    'documents_processing',
    'ai_analysis',
    'human_review',
    'pending_approval',
    'approved',
    'denied',
    'more_info_required',
    'closed',
    'escalated'
);

-- Notification channel enum
CREATE TYPE notification_channel AS ENUM (
    'email',
    'sms',
    'whatsapp',
    'slack',
    'zendesk'
);

-- Event type enum
CREATE TYPE event_type AS ENUM (
    'claim_created',
    'documents_uploaded',
    'ai_analysis_started',
    'ai_analysis_completed',
    'assigned_to_agent',
    'human_review_started',
    'decision_made',
    'notification_sent',
    'status_updated',
    'escalated',
    'closed'
);

-- Decision type enum
CREATE TYPE decision_type AS ENUM (
    'approve',
    'deny',
    'request_more_info',
    'escalate'
);

-- Document type enum
CREATE TYPE document_type AS ENUM (
    'incident_report',
    'medical_report',
    'police_report',
    'photos',
    'receipts',
    'insurance_policy',
    'other'
);

-- =============================================================================
-- CORE TABLES
-- =============================================================================

-- Claims table - Main claim records
CREATE TABLE claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_number VARCHAR(50) UNIQUE NOT NULL,
    zendesk_ticket_id BIGINT UNIQUE,
    
    -- User Information
    user_email VARCHAR(255) NOT NULL,
    user_phone VARCHAR(20),
    user_name VARCHAR(255),
    
    -- Claim Details
    subject TEXT NOT NULL,
    description TEXT,
    incident_date DATE,
    reported_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Status and Assignment
    status claim_status DEFAULT 'received',
    priority INTEGER DEFAULT 3, -- 1=urgent, 2=high, 3=normal, 4=low
    assigned_agent_id UUID,
    assigned_team VARCHAR(100),
    
    -- Financial Information
    claimed_amount DECIMAL(12,2),
    approved_amount DECIMAL(12,2),
    estimated_amount DECIMAL(12,2),
    
    -- AI Analysis
    ai_confidence_score DECIMAL(3,2), -- 0.00 to 1.00
    fraud_risk_score DECIMAL(3,2), -- 0.00 to 1.00
    complexity_score DECIMAL(3,2), -- 0.00 to 1.00
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    
    CONSTRAINT valid_amounts CHECK (
        claimed_amount >= 0 AND 
        approved_amount >= 0 AND 
        estimated_amount >= 0
    ),
    CONSTRAINT valid_scores CHECK (
        ai_confidence_score BETWEEN 0 AND 1 AND
        fraud_risk_score BETWEEN 0 AND 1 AND
        complexity_score BETWEEN 0 AND 1
    )
);

-- Agents table - Human agents who process claims
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    slack_user_id VARCHAR(50),
    zendesk_user_id BIGINT,
    
    -- Agent Details
    team VARCHAR(100),
    role VARCHAR(100),
    specializations TEXT[] DEFAULT '{}',
    
    -- Status
    is_active BOOLEAN DEFAULT true,
    current_workload INTEGER DEFAULT 0,
    max_workload INTEGER DEFAULT 10,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Claim documents table - All documents associated with claims
CREATE TABLE claim_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    
    -- File Information
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    document_type document_type DEFAULT 'other',
    
    -- OCR and Analysis
    ocr_text TEXT,
    ocr_confidence DECIMAL(3,2),
    extracted_data JSONB DEFAULT '{}',
    analysis_results JSONB DEFAULT '{}',
    
    -- Processing Status
    is_processed BOOLEAN DEFAULT false,
    processing_error TEXT,
    
    -- Timestamps
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE,
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Claim timeline table - Complete audit trail of all claim events
CREATE TABLE claim_timeline (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    
    -- Event Information
    event_type event_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    
    -- Actor Information
    actor_type VARCHAR(50) NOT NULL, -- 'system', 'agent', 'user', 'ai'
    actor_id UUID, -- agent_id if actor_type is 'agent'
    actor_name VARCHAR(255),
    actor_email VARCHAR(255),
    
    -- Event Data
    event_data JSONB DEFAULT '{}',
    previous_status claim_status,
    new_status claim_status,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Agent decisions table - Track all human decisions
CREATE TABLE agent_decisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES agents(id),
    
    -- Decision Information
    decision_type decision_type NOT NULL,
    reasoning TEXT NOT NULL,
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 5),
    
    -- Financial Impact
    approved_amount DECIMAL(12,2),
    adjustment_reason TEXT,
    
    -- Review Information
    review_time_minutes INTEGER,
    ai_recommendation_followed BOOLEAN,
    ai_recommendation_override_reason TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Notifications table - Track all notifications sent
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    
    -- Notification Details
    channel notification_channel NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    template_name VARCHAR(100),
    
    -- Status
    status VARCHAR(50) DEFAULT 'pending', -- pending, sent, delivered, failed
    sent_at TIMESTAMP WITH TIME ZONE,
    delivered_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    
    -- External IDs
    external_id VARCHAR(255), -- SMS ID, email ID, etc.
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- Zendesk integration table - Track Zendesk ticket synchronization
CREATE TABLE zendesk_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    ticket_id BIGINT UNIQUE NOT NULL,
    
    -- Ticket Information
    ticket_url TEXT NOT NULL,
    status VARCHAR(50) NOT NULL,
    priority VARCHAR(50),
    
    -- Synchronization
    last_sync_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sync_status VARCHAR(50) DEFAULT 'synced', -- synced, pending, error
    sync_error TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- AI analysis results table - Store detailed AI analysis
CREATE TABLE ai_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,

    -- Analysis Information
    analysis_type VARCHAR(100) NOT NULL, -- 'initial', 'detailed', 'fraud_check'
    model_version VARCHAR(50) NOT NULL,

    -- Results
    results JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(3,2) NOT NULL,
    recommendations TEXT[],
    risk_factors TEXT[],

    -- Processing Information
    processing_time_ms INTEGER,
    tokens_used INTEGER,

    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Metadata
    metadata JSONB DEFAULT '{}'
);

-- =============================================================================
-- INDEXES FOR PERFORMANCE
-- =============================================================================

-- Claims table indexes
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_claims_user_email ON claims(user_email);
CREATE INDEX idx_claims_assigned_agent ON claims(assigned_agent_id);
CREATE INDEX idx_claims_created_at ON claims(created_at);
CREATE INDEX idx_claims_zendesk_ticket ON claims(zendesk_ticket_id);
CREATE INDEX idx_claims_claim_number ON claims(claim_number);

-- Timeline table indexes
CREATE INDEX idx_timeline_claim_id ON claim_timeline(claim_id);
CREATE INDEX idx_timeline_event_type ON claim_timeline(event_type);
CREATE INDEX idx_timeline_created_at ON claim_timeline(created_at);
CREATE INDEX idx_timeline_actor ON claim_timeline(actor_type, actor_id);

-- Documents table indexes
CREATE INDEX idx_documents_claim_id ON claim_documents(claim_id);
CREATE INDEX idx_documents_type ON claim_documents(document_type);
CREATE INDEX idx_documents_processed ON claim_documents(is_processed);

-- Notifications table indexes
CREATE INDEX idx_notifications_claim_id ON notifications(claim_id);
CREATE INDEX idx_notifications_channel ON notifications(channel);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Agent decisions table indexes
CREATE INDEX idx_decisions_claim_id ON agent_decisions(claim_id);
CREATE INDEX idx_decisions_agent_id ON agent_decisions(agent_id);
CREATE INDEX idx_decisions_created_at ON agent_decisions(created_at);

-- AI analysis table indexes
CREATE INDEX idx_ai_analysis_claim_id ON ai_analysis(claim_id);
CREATE INDEX idx_ai_analysis_type ON ai_analysis(analysis_type);
CREATE INDEX idx_ai_analysis_created_at ON ai_analysis(created_at);

-- =============================================================================
-- TRIGGERS FOR AUTOMATIC UPDATES
-- =============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at trigger to relevant tables
CREATE TRIGGER update_claims_updated_at
    BEFORE UPDATE ON claims
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agents_updated_at
    BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_zendesk_tickets_updated_at
    BEFORE UPDATE ON zendesk_tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to create timeline entry on claim status change
CREATE OR REPLACE FUNCTION create_status_timeline_entry()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO claim_timeline (
            claim_id,
            event_type,
            title,
            description,
            actor_type,
            previous_status,
            new_status,
            event_data
        ) VALUES (
            NEW.id,
            'status_updated',
            'Claim status changed',
            'Status changed from ' || COALESCE(OLD.status::text, 'null') || ' to ' || NEW.status::text,
            'system',
            OLD.status,
            NEW.status,
            jsonb_build_object(
                'previous_status', OLD.status,
                'new_status', NEW.status,
                'changed_at', NOW()
            )
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply status change trigger
CREATE TRIGGER create_claim_status_timeline
    AFTER UPDATE ON claims
    FOR EACH ROW EXECUTE FUNCTION create_status_timeline_entry();

-- Function to update agent workload
CREATE OR REPLACE FUNCTION update_agent_workload()
RETURNS TRIGGER AS $$
BEGIN
    -- Decrease workload for old agent
    IF OLD.assigned_agent_id IS NOT NULL THEN
        UPDATE agents
        SET current_workload = current_workload - 1
        WHERE id = OLD.assigned_agent_id;
    END IF;

    -- Increase workload for new agent
    IF NEW.assigned_agent_id IS NOT NULL THEN
        UPDATE agents
        SET current_workload = current_workload + 1,
            last_active_at = NOW()
        WHERE id = NEW.assigned_agent_id;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply agent workload trigger
CREATE TRIGGER update_agent_workload_on_assignment
    AFTER UPDATE OF assigned_agent_id ON claims
    FOR EACH ROW EXECUTE FUNCTION update_agent_workload();

-- =============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =============================================================================

-- Enable RLS on all tables
ALTER TABLE claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE agents ENABLE ROW LEVEL SECURITY;
ALTER TABLE claim_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE claim_timeline ENABLE ROW LEVEL SECURITY;
ALTER TABLE agent_decisions ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE zendesk_tickets ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_analysis ENABLE ROW LEVEL SECURITY;

-- Claims policies
CREATE POLICY "Users can view their own claims" ON claims
    FOR SELECT USING (user_email = auth.jwt() ->> 'email');

CREATE POLICY "Agents can view assigned claims" ON claims
    FOR SELECT USING (
        assigned_agent_id = (auth.jwt() ->> 'sub')::uuid OR
        auth.jwt() ->> 'role' = 'admin'
    );

CREATE POLICY "Service role can manage all claims" ON claims
    FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- Documents policies
CREATE POLICY "Users can view documents for their claims" ON claim_documents
    FOR SELECT USING (
        claim_id IN (
            SELECT id FROM claims WHERE user_email = auth.jwt() ->> 'email'
        )
    );

CREATE POLICY "Agents can view documents for assigned claims" ON claim_documents
    FOR SELECT USING (
        claim_id IN (
            SELECT id FROM claims
            WHERE assigned_agent_id = (auth.jwt() ->> 'sub')::uuid
               OR auth.jwt() ->> 'role' = 'admin'
        )
    );

-- Timeline policies
CREATE POLICY "Users can view timeline for their claims" ON claim_timeline
    FOR SELECT USING (
        claim_id IN (
            SELECT id FROM claims WHERE user_email = auth.jwt() ->> 'email'
        )
    );

CREATE POLICY "Agents can view timeline for assigned claims" ON claim_timeline
    FOR SELECT USING (
        claim_id IN (
            SELECT id FROM claims
            WHERE assigned_agent_id = (auth.jwt() ->> 'sub')::uuid
               OR auth.jwt() ->> 'role' = 'admin'
        )
    );

-- Service role policies for all tables
CREATE POLICY "Service role full access" ON agents FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access" ON claim_documents FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access" ON claim_timeline FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access" ON agent_decisions FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access" ON notifications FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access" ON zendesk_tickets FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');
CREATE POLICY "Service role full access" ON ai_analysis FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

-- =============================================================================
-- VIEWS FOR COMMON QUERIES
-- =============================================================================

-- Comprehensive claim view with all related data
CREATE VIEW claim_details AS
SELECT
    c.*,
    a.name as assigned_agent_name,
    a.email as assigned_agent_email,
    a.team as assigned_team,
    zt.ticket_id as zendesk_ticket_id,
    zt.ticket_url as zendesk_url,
    COUNT(cd.id) as document_count,
    COUNT(CASE WHEN cd.is_processed THEN 1 END) as processed_document_count,
    MAX(ai.confidence_score) as latest_ai_confidence,
    COUNT(n.id) as notification_count,
    COUNT(CASE WHEN n.status = 'delivered' THEN 1 END) as delivered_notification_count
FROM claims c
LEFT JOIN agents a ON c.assigned_agent_id = a.id
LEFT JOIN zendesk_tickets zt ON c.id = zt.claim_id
LEFT JOIN claim_documents cd ON c.id = cd.claim_id
LEFT JOIN ai_analysis ai ON c.id = ai.claim_id
LEFT JOIN notifications n ON c.id = n.claim_id
GROUP BY c.id, a.id, zt.id;

-- Agent workload view
CREATE VIEW agent_workload AS
SELECT
    a.*,
    COUNT(c.id) as active_claims,
    AVG(c.complexity_score) as avg_complexity,
    COUNT(CASE WHEN c.priority = 1 THEN 1 END) as urgent_claims,
    COUNT(CASE WHEN c.status = 'human_review' THEN 1 END) as pending_review_claims
FROM agents a
LEFT JOIN claims c ON a.id = c.assigned_agent_id AND c.status NOT IN ('closed', 'denied')
WHERE a.is_active = true
GROUP BY a.id;

-- Claim timeline with actor details
CREATE VIEW claim_timeline_detailed AS
SELECT
    ct.*,
    c.claim_number,
    c.user_email,
    CASE
        WHEN ct.actor_type = 'agent' THEN a.name
        ELSE ct.actor_name
    END as actor_display_name
FROM claim_timeline ct
JOIN claims c ON ct.claim_id = c.id
LEFT JOIN agents a ON ct.actor_id = a.id AND ct.actor_type = 'agent';

-- =============================================================================
-- UTILITY FUNCTIONS
-- =============================================================================

-- Function to generate claim number
CREATE OR REPLACE FUNCTION generate_claim_number()
RETURNS TEXT AS $$
DECLARE
    year_part TEXT;
    sequence_part TEXT;
    claim_number TEXT;
BEGIN
    year_part := EXTRACT(YEAR FROM NOW())::TEXT;

    -- Get next sequence number for this year
    SELECT LPAD((COUNT(*) + 1)::TEXT, 6, '0') INTO sequence_part
    FROM claims
    WHERE EXTRACT(YEAR FROM created_at) = EXTRACT(YEAR FROM NOW());

    claim_number := 'CLAIM-' || year_part || '-' || sequence_part;

    RETURN claim_number;
END;
$$ LANGUAGE plpgsql;

-- Function to assign claim to best available agent
CREATE OR REPLACE FUNCTION assign_claim_to_agent(claim_uuid UUID, preferred_team TEXT DEFAULT NULL)
RETURNS UUID AS $$
DECLARE
    selected_agent_id UUID;
BEGIN
    -- Find the best available agent
    SELECT id INTO selected_agent_id
    FROM agents
    WHERE is_active = true
      AND current_workload < max_workload
      AND (preferred_team IS NULL OR team = preferred_team)
    ORDER BY
        current_workload ASC,
        last_active_at DESC
    LIMIT 1;

    -- Update the claim with the selected agent
    IF selected_agent_id IS NOT NULL THEN
        UPDATE claims
        SET assigned_agent_id = selected_agent_id,
            status = 'human_review'
        WHERE id = claim_uuid;

        -- Create timeline entry
        INSERT INTO claim_timeline (
            claim_id,
            event_type,
            title,
            description,
            actor_type,
            actor_id,
            event_data
        ) VALUES (
            claim_uuid,
            'assigned_to_agent',
            'Claim assigned to agent',
            'Claim automatically assigned to available agent',
            'system',
            NULL,
            jsonb_build_object(
                'agent_id', selected_agent_id,
                'assignment_method', 'automatic'
            )
        );
    END IF;

    RETURN selected_agent_id;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate SLA status
CREATE OR REPLACE FUNCTION get_claim_sla_status(claim_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    claim_record RECORD;
    sla_status JSONB;
    initial_response_sla INTEGER := 15; -- minutes
    analysis_sla INTEGER := 120; -- minutes
    decision_sla INTEGER := 240; -- minutes
BEGIN
    SELECT * INTO claim_record FROM claims WHERE id = claim_uuid;

    sla_status := jsonb_build_object(
        'initial_response', jsonb_build_object(
            'sla_minutes', initial_response_sla,
            'elapsed_minutes', EXTRACT(EPOCH FROM (NOW() - claim_record.created_at))/60,
            'is_breached', EXTRACT(EPOCH FROM (NOW() - claim_record.created_at))/60 > initial_response_sla
        ),
        'analysis_completion', jsonb_build_object(
            'sla_minutes', analysis_sla,
            'elapsed_minutes', EXTRACT(EPOCH FROM (NOW() - claim_record.created_at))/60,
            'is_breached', EXTRACT(EPOCH FROM (NOW() - claim_record.created_at))/60 > analysis_sla
        ),
        'decision', jsonb_build_object(
            'sla_minutes', decision_sla,
            'elapsed_minutes', EXTRACT(EPOCH FROM (NOW() - claim_record.created_at))/60,
            'is_breached', EXTRACT(EPOCH FROM (NOW() - claim_record.created_at))/60 > decision_sla
        )
    );

    RETURN sla_status;
END;
$$ LANGUAGE plpgsql;

-- =============================================================================
-- INITIAL DATA
-- =============================================================================

-- Insert default agent for demo
INSERT INTO agents (id, email, name, team, role, specializations) VALUES
(uuid_generate_v4(), '<EMAIL>', 'Demo Agent', 'Claims Processing', 'Senior Adjuster', ARRAY['auto', 'property', 'liability']);

-- Create indexes on JSONB columns for better performance
CREATE INDEX idx_claims_metadata_gin ON claims USING GIN (metadata);
CREATE INDEX idx_claim_documents_extracted_data_gin ON claim_documents USING GIN (extracted_data);
CREATE INDEX idx_ai_analysis_results_gin ON ai_analysis USING GIN (results);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO service_role;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO service_role;
