# ARIA System Environment Configuration

# Database Configuration
DATABASE_URL=postgresql://aria_user:aria_password@localhost:5432/aria_db
REDIS_URL=redis://localhost:6379/0

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview

# HumanLayer Configuration
HUMANLAYER_API_KEY=your_humanlayer_api_key_here
HUMANLAYER_RUN_ID=aria-claims-processor

# Email Configuration
CLAIMS_EMAIL=<EMAIL>
CLAIMS_PASSWORD=your_email_password
IMAP_SERVER=imap.gmail.com
SMTP_SERVER=smtp.gmail.com
IMAP_PORT=993
SMTP_PORT=587

# Azure Services
AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
AZURE_CV_ENDPOINT=your_azure_computer_vision_endpoint
AZURE_CV_KEY=your_azure_computer_vision_key
AZURE_COMMUNICATION_CONNECTION_STRING=your_azure_communication_connection_string
AZURE_EMAIL_SENDER=<EMAIL>

# AWS Services
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=aria-documents
AWS_EMAIL_SENDER=<EMAIL>

# Google Cloud Services
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/gcp-credentials.json
GCP_PROJECT_ID=your-gcp-project-id
GCP_PROCESSOR_NAME=projects/your-project/locations/us/processors/your-processor-id
GCP_STORAGE_BUCKET=aria-documents-gcp

# Application Configuration
APP_NAME=ARIA Claims Processor
APP_VERSION=1.0.0
DEBUG=true
LOG_LEVEL=INFO
ENVIRONMENT=development

# Dashboard Configuration
DASHBOARD_HOST=localhost
DASHBOARD_PORT=8501
API_HOST=localhost
API_PORT=8000

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090
HEALTH_CHECK_INTERVAL=30

# Feature Flags
ENABLE_MULTI_CLOUD=true
ENABLE_AUTO_APPROVAL=true
ENABLE_FRAUD_DETECTION=true
ENABLE_REAL_TIME_UPDATES=true
