[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "aria-system"
version = "1.0.0"
description = "ARIA - Autonomous Risk Intelligence Agent for Insurance Claims Processing"
authors = [
    {name = "ARIA Development Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.9"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "sqlalchemy>=2.0.23",
    "openai>=1.3.7",
    "langchain>=0.0.350",
    "humanlayer>=0.3.0",
    "streamlit>=1.28.2",
    "plotly>=5.17.0",
    "pandas>=2.1.4",
    "redis>=5.0.1",
    "loguru>=0.7.2",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.1",
    "pre-commit>=3.5.0",
]

cloud = [
    "azure-storage-blob>=12.19.0",
    "azure-cognitiveservices-vision-computervision>=0.9.0",
    "boto3>=1.34.0",
    "google-cloud-documentai>=2.20.1",
]

[project.urls]
Homepage = "https://github.com/aria-system/aria"
Documentation = "https://aria-system.readthedocs.io"
Repository = "https://github.com/aria-system/aria.git"
Issues = "https://github.com/aria-system/aria/issues"

[project.scripts]
aria = "aria_system.cli:main"
aria-dashboard = "aria_system.dashboard.main:run"
aria-worker = "aria_system.workers.main:run"

[tool.setuptools.packages.find]
where = ["."]
include = ["aria_system*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["aria_system"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "azure.*",
    "boto3.*",
    "google.*",
    "humanlayer.*",
    "baml_py.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "-ra",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]
asyncio_mode = "auto"
