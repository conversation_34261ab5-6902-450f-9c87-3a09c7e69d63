"""
Demo scenarios and test data for ARIA system.
Provides realistic test cases for demonstration purposes.
"""

import pytest
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json

from aria_system.models.claims import Claim, ClaimStatus, ClaimType
from aria_system.models.documents import Document, DocumentType
from aria_system.services.ai_agent_service import AIAgentService
from aria_system.services.human_interaction_service import HumanInteractionService, ExpertType


class DemoScenarios:
    """Demo scenarios for ARIA system testing and demonstration."""
    
    @staticmethod
    def get_standard_auto_claim() -> Dict[str, Any]:
        """Standard auto liability claim - should auto-approve."""
        return {
            'claim_number': 'DEMO-AUTO-001',
            'sender_name': '<PERSON>',
            'sender_email': '<EMAIL>',
            'subject': 'Auto Accident Claim - Policy ABC123456',
            'body': """
            Dear Claims Department,
            
            I am writing to report an auto accident that occurred on {(datetime.now() - timedelta(days=7)).strftime('%B %d, %Y')}, at approximately 2:30 PM
            at the intersection of Main Street and Oak Avenue in Toronto, ON.
            
            I was driving my 2020 Honda Civic (License: ABC 123) when another vehicle ran a red light and 
            collided with the passenger side of my car. The other driver admitted fault at the scene.
            
            The damage to my vehicle includes:
            - Passenger side door damage
            - Side mirror replacement needed
            - Paint scratches along the side panel
            
            I have attached photos of the damage and the police report. The estimated repair cost from 
            AutoFix Garage is $8,500.
            
            My policy number is ABC123456. Please let me know what additional information you need.
            
            Thank you,
            Sarah Johnson
            Phone: (*************
            """,
            'claim_info': {
                'claim_type': 'auto_liability',
                'policy_number': 'ABC123456',
                'incident_date': (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d'),
                'estimated_amount': 8500
            },
            'attachments': [
                {
                    'filename': 'damage_photos.jpg',
                    'size': 2048000,
                    'content_type': 'image/jpeg',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/damage_photos.jpg'}
                },
                {
                    'filename': 'police_report.pdf',
                    'size': 512000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/police_report.pdf'}
                }
            ],
            'expected_outcome': {
                'coverage_decision': 'covered',
                'auto_approved': True,
                'settlement_range': (6800, 8500),
                'processing_time_minutes': 25
            }
        }
    
    @staticmethod
    def get_complex_bodily_injury_claim() -> Dict[str, Any]:
        """Complex bodily injury claim - requires human review."""
        return {
            'claim_number': 'DEMO-INJURY-002',
            'sender_name': 'Michael Chen',
            'sender_email': '<EMAIL>',
            'subject': 'Bodily Injury Claim - Slip and Fall Incident',
            'body': """
            To Whom It May Concern,
            
            I am filing a claim for a slip and fall incident that occurred at Metro Shopping Center 
            on {(datetime.now() - timedelta(days=12)).strftime('%B %d, %Y')}, at approximately 6:45 PM.
            
            I was walking through the main corridor when I slipped on a wet floor near the food court. 
            There were no warning signs posted, and the area was not cordoned off. Several witnesses 
            saw the incident occur.
            
            As a result of the fall, I sustained:
            - Fractured left wrist requiring surgery
            - Severe bruising on left hip and shoulder
            - Ongoing back pain and mobility issues
            
            I have been receiving treatment at Toronto General Hospital and will require ongoing 
            physiotherapy. My orthopedic surgeon estimates 6-8 weeks of recovery time.
            
            Medical expenses to date: $15,000
            Lost wages (4 weeks): $8,000
            Estimated future medical costs: $12,000
            
            Total claim amount: $35,000
            
            Policy Number: XYZ789012
            
            I have attached medical records, witness statements, and photos of the incident scene.
            
            Please contact me to discuss this matter further.
            
            Michael Chen
            Phone: (*************
            """,
            'claim_info': {
                'claim_type': 'bodily_injury',
                'policy_number': 'XYZ789012',
                'incident_date': (datetime.now() - timedelta(days=12)).strftime('%Y-%m-%d'),
                'estimated_amount': 35000
            },
            'attachments': [
                {
                    'filename': 'medical_records.pdf',
                    'size': 1024000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/medical_records.pdf'}
                },
                {
                    'filename': 'witness_statements.pdf',
                    'size': 256000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/witness_statements.pdf'}
                },
                {
                    'filename': 'incident_photos.jpg',
                    'size': 3072000,
                    'content_type': 'image/jpeg',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/incident_photos.jpg'}
                }
            ],
            'expected_outcome': {
                'coverage_decision': 'requires_investigation',
                'requires_human_review': True,
                'expert_type': 'senior_adjuster',
                'settlement_range': (25000, 40000),
                'processing_time_minutes': 120
            }
        }
    
    @staticmethod
    def get_high_value_property_claim() -> Dict[str, Any]:
        """High-value property damage claim - requires manager approval."""
        return {
            'claim_number': 'DEMO-PROPERTY-003',
            'sender_name': 'Jennifer Rodriguez',
            'sender_email': '<EMAIL>',
            'subject': 'Water Damage Claim - Burst Pipe Incident',
            'body': """
            Dear Claims Team,
            
            I am submitting a claim for extensive water damage to my home at 123 Maple Street, 
            Toronto, ON M5V 2A1, which occurred on {(datetime.now() - timedelta(days=14)).strftime('%B %d, %Y')}.
            
            A main water pipe burst in the basement during the recent cold snap, causing significant 
            flooding throughout the basement and first floor of my home. The water damage affected:
            
            - Hardwood flooring throughout main floor (1,200 sq ft)
            - Kitchen cabinets and appliances
            - Living room furniture and electronics
            - Basement finishing and storage items
            - HVAC system damage
            
            I immediately contacted a water damage restoration company (AquaRestore Inc.) who 
            provided emergency services and a detailed assessment. Their estimate for repairs 
            and restoration is $85,000.
            
            Additional costs:
            - Temporary accommodation (hotel): $3,000
            - Storage for salvageable items: $1,500
            - Emergency plumbing repairs: $2,800
            
            Total claim amount: $92,300
            
            Policy Number: HOME456789
            Deductible: $2,500
            
            I have attached the restoration company's report, photos of the damage, receipts for 
            emergency expenses, and the plumber's report on the pipe failure.
            
            This has been a very stressful situation, and I appreciate your prompt attention to 
            this matter.
            
            Best regards,
            Jennifer Rodriguez
            Phone: (*************
            """,
            'claim_info': {
                'claim_type': 'property_damage',
                'policy_number': 'HOME456789',
                'incident_date': (datetime.now() - timedelta(days=14)).strftime('%Y-%m-%d'),
                'estimated_amount': 92300
            },
            'attachments': [
                {
                    'filename': 'restoration_estimate.pdf',
                    'size': 2048000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/restoration_estimate.pdf'}
                },
                {
                    'filename': 'damage_photos.zip',
                    'size': 15728640,
                    'content_type': 'application/zip',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/damage_photos.zip'}
                },
                {
                    'filename': 'plumber_report.pdf',
                    'size': 512000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/plumber_report.pdf'}
                },
                {
                    'filename': 'emergency_receipts.pdf',
                    'size': 256000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/emergency_receipts.pdf'}
                }
            ],
            'expected_outcome': {
                'coverage_decision': 'covered',
                'requires_human_review': True,
                'expert_type': 'manager_approver',
                'settlement_range': (75000, 90000),
                'processing_time_minutes': 180
            }
        }
    
    @staticmethod
    def get_suspicious_fraud_claim() -> Dict[str, Any]:
        """Suspicious claim with fraud indicators - requires investigation."""
        return {
            'claim_number': 'DEMO-FRAUD-004',
            'sender_name': 'Robert Smith',
            'sender_email': '<EMAIL>',
            'subject': 'Urgent Auto Theft Claim',
            'body': """
            Hello,
            
            My car was stolen last night from downtown Toronto. I parked it at 11 PM and when I 
            came back at 7 AM it was gone. The police have been notified.
            
            Vehicle: {datetime.now().year} BMW X5 (Brand new, purchased 2 weeks ago)
            License: STOLEN1
            VIN: 1HGBH41JXMN109186
            Value: $95,000
            
            I need immediate settlement as I have no other transportation. I can provide all 
            necessary documentation.
            
            Policy: QUICK123
            
            Please process this urgently.
            
            R. Smith
            """,
            'claim_info': {
                'claim_type': 'auto_liability',
                'policy_number': 'QUICK123',
                'incident_date': (datetime.now() - timedelta(days=6)).strftime('%Y-%m-%d'),
                'estimated_amount': 95000
            },
            'attachments': [
                {
                    'filename': 'police_report_theft.pdf',
                    'size': 128000,
                    'content_type': 'application/pdf',
                    'storage_urls': {'azure': 'https://demo.blob.core.windows.net/claims/police_report_theft.pdf'}
                }
            ],
            'fraud_indicators': [
                'Recently purchased high-value vehicle',
                'Urgent settlement request',
                'Minimal documentation provided',
                'Suspicious email address pattern',
                'High claim amount relative to policy'
            ],
            'expected_outcome': {
                'coverage_decision': 'requires_investigation',
                'requires_human_review': True,
                'expert_type': 'fraud_investigator',
                'processing_time_minutes': 300
            }
        }
    
    @staticmethod
    def get_all_demo_scenarios() -> List[Dict[str, Any]]:
        """Get all demo scenarios."""
        return [
            DemoScenarios.get_standard_auto_claim(),
            DemoScenarios.get_complex_bodily_injury_claim(),
            DemoScenarios.get_high_value_property_claim(),
            DemoScenarios.get_suspicious_fraud_claim()
        ]


class TestDataGenerator:
    """Generate test data for ARIA system testing."""
    
    @staticmethod
    def generate_sample_documents() -> List[Dict[str, Any]]:
        """Generate sample document data."""
        return [
            {
                'filename': 'police_report_001.pdf',
                'document_type': DocumentType.POLICE_REPORT,
                'extracted_text': """
                POLICE INCIDENT REPORT
                Report Number: TR-{datetime.now().year}-001234
                Date: {(datetime.now() - timedelta(days=7)).strftime('%B %d, %Y')}
                Time: 14:30
                Location: Main St & Oak Ave, Toronto, ON
                
                INCIDENT SUMMARY:
                Motor vehicle collision involving two vehicles. Driver of Vehicle 2 
                failed to stop at red light, resulting in collision with Vehicle 1.
                
                VEHICLE 1:
                Driver: Sarah Johnson
                License: ABC 123
                Make/Model: 2020 Honda Civic
                
                VEHICLE 2:
                Driver: John Doe
                License: XYZ 789
                Make/Model: 2019 Ford F-150
                
                Driver of Vehicle 2 admitted fault at scene.
                No injuries reported.
                """,
                'entities': [
                    {'type': 'dates', 'value': (datetime.now() - timedelta(days=7)).strftime('%B %d, %Y')},
                    {'type': 'policy_numbers', 'value': f'TR-{datetime.now().year}-001234'},
                    {'type': 'vehicle_info', 'value': '2020 Honda Civic'},
                    {'type': 'vehicle_info', 'value': '2019 Ford F-150'}
                ]
            },
            {
                'filename': 'repair_estimate_001.pdf',
                'document_type': DocumentType.REPAIR_ESTIMATE,
                'extracted_text': """
                AUTOFIX GARAGE
                REPAIR ESTIMATE
                
                Customer: Sarah Johnson
                Vehicle: 2020 Honda Civic
                License: ABC 123
                Date: {(datetime.now() - timedelta(days=6)).strftime('%B %d, %Y')}
                
                DAMAGE ASSESSMENT:
                - Passenger door replacement: $2,500
                - Side mirror replacement: $350
                - Paint work (side panel): $1,200
                - Labor (8 hours): $800
                
                PARTS:
                Door assembly: $1,800
                Mirror assembly: $200
                Paint materials: $400
                
                TOTAL ESTIMATE: $8,500
                """,
                'entities': [
                    {'type': 'monetary_amounts', 'value': '2500'},
                    {'type': 'monetary_amounts', 'value': '8500'},
                    {'type': 'dates', 'value': (datetime.now() - timedelta(days=6)).strftime('%B %d, %Y')},
                    {'type': 'vehicle_info', 'value': '2020 Honda Civic'}
                ]
            }
        ]
    
    @staticmethod
    def generate_performance_metrics() -> Dict[str, Any]:
        """Generate sample performance metrics."""
        return {
            'processing_times': {
                'auto_liability': {'avg': 28, 'min': 15, 'max': 45},
                'property_damage': {'avg': 65, 'min': 30, 'max': 120},
                'bodily_injury': {'avg': 95, 'min': 60, 'max': 180}
            },
            'ai_accuracy': {
                'coverage_decisions': 0.968,
                'settlement_estimates': 0.892,
                'fraud_detection': 0.934
            },
            'human_review_rates': {
                'auto_liability': 0.05,
                'property_damage': 0.12,
                'bodily_injury': 0.35,
                'high_value': 0.85
            },
            'customer_satisfaction': {
                'overall': 0.91,
                'processing_speed': 0.94,
                'communication': 0.88,
                'decision_quality': 0.92
            }
        }


@pytest.mark.asyncio
class TestDemoScenarios:
    """Test demo scenarios to validate system functionality."""
    
    async def test_standard_auto_claim_processing(self):
        """Test processing of standard auto claim."""
        scenario = DemoScenarios.get_standard_auto_claim()
        
        # Simulate claim processing
        expected = scenario['expected_outcome']
        
        assert scenario['claim_info']['claim_type'] == 'auto_liability'
        assert scenario['claim_info']['estimated_amount'] == 8500
        assert expected['auto_approved'] == True
        assert expected['coverage_decision'] == 'covered'
    
    async def test_complex_injury_claim_processing(self):
        """Test processing of complex bodily injury claim."""
        scenario = DemoScenarios.get_complex_bodily_injury_claim()
        
        expected = scenario['expected_outcome']
        
        assert scenario['claim_info']['claim_type'] == 'bodily_injury'
        assert scenario['claim_info']['estimated_amount'] == 35000
        assert expected['requires_human_review'] == True
        assert expected['expert_type'] == 'senior_adjuster'
    
    async def test_high_value_property_claim(self):
        """Test processing of high-value property claim."""
        scenario = DemoScenarios.get_high_value_property_claim()
        
        expected = scenario['expected_outcome']
        
        assert scenario['claim_info']['estimated_amount'] > 50000
        assert expected['requires_human_review'] == True
        assert expected['expert_type'] == 'manager_approver'
    
    async def test_fraud_detection_scenario(self):
        """Test fraud detection capabilities."""
        scenario = DemoScenarios.get_suspicious_fraud_claim()
        
        assert 'fraud_indicators' in scenario
        assert len(scenario['fraud_indicators']) > 0
        assert scenario['expected_outcome']['expert_type'] == 'fraud_investigator'
    
    def test_demo_data_completeness(self):
        """Test that all demo scenarios have required fields."""
        scenarios = DemoScenarios.get_all_demo_scenarios()
        
        required_fields = [
            'claim_number', 'sender_name', 'sender_email', 
            'subject', 'body', 'claim_info', 'expected_outcome'
        ]
        
        for scenario in scenarios:
            for field in required_fields:
                assert field in scenario, f"Missing field {field} in scenario {scenario.get('claim_number', 'unknown')}"
    
    def test_document_test_data(self):
        """Test document test data generation."""
        documents = TestDataGenerator.generate_sample_documents()
        
        assert len(documents) > 0
        
        for doc in documents:
            assert 'filename' in doc
            assert 'document_type' in doc
            assert 'extracted_text' in doc
            assert 'entities' in doc
    
    def test_performance_metrics_data(self):
        """Test performance metrics data structure."""
        metrics = TestDataGenerator.generate_performance_metrics()
        
        required_sections = [
            'processing_times', 'ai_accuracy', 
            'human_review_rates', 'customer_satisfaction'
        ]
        
        for section in required_sections:
            assert section in metrics
            assert isinstance(metrics[section], dict)


if __name__ == "__main__":
    # Run demo scenarios
    print("🎬 ARIA Demo Scenarios")
    print("=" * 50)
    
    scenarios = DemoScenarios.get_all_demo_scenarios()
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['claim_number']}")
        print(f"   Type: {scenario['claim_info']['claim_type']}")
        print(f"   Amount: ${scenario['claim_info']['estimated_amount']:,}")
        print(f"   Expected: {scenario['expected_outcome']['coverage_decision']}")
        
        if scenario['expected_outcome'].get('requires_human_review'):
            expert = scenario['expected_outcome'].get('expert_type', 'unknown')
            print(f"   Expert: {expert}")
    
    print(f"\n📊 Performance Metrics:")
    metrics = TestDataGenerator.generate_performance_metrics()
    print(f"   AI Accuracy: {metrics['ai_accuracy']['coverage_decisions']:.1%}")
    print(f"   Avg Processing: {metrics['processing_times']['auto_liability']['avg']} min")
    print(f"   Customer Satisfaction: {metrics['customer_satisfaction']['overall']:.1%}")
    
    print("\n✅ Demo data ready for testing!")
