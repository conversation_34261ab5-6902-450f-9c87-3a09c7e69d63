-- ARIA Claims Processing System - Quick Setup Schema
-- Essential tables for immediate functionality

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Claim status enum
CREATE TYPE claim_status AS ENUM (
    'received', 'documents_processing', 'ai_analysis', 'human_review',
    'pending_approval', 'approved', 'denied', 'more_info_required', 'closed', 'escalated'
);

-- Notification channel enum
CREATE TYPE notification_channel AS ENUM ('email', 'sms', 'whatsapp', 'slack', 'zendesk');

-- Event type enum
CREATE TYPE event_type AS ENUM (
    'claim_created', 'documents_uploaded', 'ai_analysis_completed',
    'assigned_to_agent', 'decision_made', 'notification_sent', 'status_updated'
);

-- Decision type enum
CREATE TYPE decision_type AS ENUM ('approve', 'deny', 'request_more_info', 'escalate');

-- Document type enum
CREATE TYPE document_type AS ENUM (
    'incident_report', 'medical_report', 'police_report', 'photos', 'receipts', 'other'
);

-- Claims table - Main claim records
CREATE TABLE claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_number VARCHAR(50) UNIQUE NOT NULL,
    zendesk_ticket_id BIGINT UNIQUE,
    user_email VARCHAR(255) NOT NULL,
    user_phone VARCHAR(20),
    user_name VARCHAR(255),
    subject TEXT NOT NULL,
    description TEXT,
    incident_date DATE,
    reported_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status claim_status DEFAULT 'received',
    priority INTEGER DEFAULT 3,
    assigned_agent_id UUID,
    claimed_amount DECIMAL(12,2),
    approved_amount DECIMAL(12,2),
    ai_confidence_score DECIMAL(3,2),
    fraud_risk_score DECIMAL(3,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Agents table
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    team VARCHAR(100),
    role VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    current_workload INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Claim documents table
CREATE TABLE claim_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    document_type document_type DEFAULT 'other',
    ocr_text TEXT,
    ocr_confidence DECIMAL(3,2),
    is_processed BOOLEAN DEFAULT false,
    uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    processed_at TIMESTAMP WITH TIME ZONE
);

-- Claim timeline table
CREATE TABLE claim_timeline (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    event_type event_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    actor_type VARCHAR(50) NOT NULL,
    actor_id UUID,
    actor_name VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    metadata JSONB DEFAULT '{}'
);

-- Agent decisions table
CREATE TABLE agent_decisions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES agents(id),
    decision_type decision_type NOT NULL,
    reasoning TEXT NOT NULL,
    confidence_level INTEGER CHECK (confidence_level BETWEEN 1 AND 5),
    approved_amount DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    channel notification_channel NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI analysis table
CREATE TABLE ai_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    analysis_type VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    results JSONB NOT NULL DEFAULT '{}',
    confidence_score DECIMAL(3,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Essential indexes
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_claims_user_email ON claims(user_email);
CREATE INDEX idx_claims_created_at ON claims(created_at);
CREATE INDEX idx_timeline_claim_id ON claim_timeline(claim_id);
CREATE INDEX idx_documents_claim_id ON claim_documents(claim_id);
CREATE INDEX idx_notifications_claim_id ON notifications(claim_id);

-- Auto-update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply timestamp triggers
CREATE TRIGGER update_claims_updated_at
    BEFORE UPDATE ON claims
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_agents_updated_at
    BEFORE UPDATE ON agents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert demo agent
INSERT INTO agents (email, name, team, role) VALUES 
('<EMAIL>', 'Demo Agent', 'Claims Processing', 'Senior Agent');

-- Success message
SELECT 'ARIA database schema applied successfully! System ready for claims processing.' as status;
