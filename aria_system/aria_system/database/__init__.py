"""
ARIA Database Package
Database connections and models
"""

import asyncio
import logging
from typing import AsyncGenerator
from .supabase_client import SupabaseClient

logger = logging.getLogger(__name__)

# Global database client
_db_client: SupabaseClient = None


async def init_database(settings):
    """Initialize database connection"""
    global _db_client
    try:
        _db_client = SupabaseClient(settings)
        await _db_client.initialize_schema()
        logger.info("Database initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False


async def get_async_db() -> AsyncGenerator[SupabaseClient, None]:
    """Get async database session"""
    if _db_client is None:
        raise RuntimeError("Database not initialized")

    try:
        yield _db_client
    except Exception as e:
        logger.error(f"Database session error: {e}")
        raise


def get_db_client() -> SupabaseClient:
    """Get database client"""
    if _db_client is None:
        raise RuntimeError("Database not initialized")
    return _db_client
