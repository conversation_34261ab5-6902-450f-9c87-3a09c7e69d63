"""
ARIA Supabase Database Client
Complete database operations for claims processing
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
import uuid

from supabase import create_client, Client
import asyncpg

from ..config import Settings

logger = logging.getLogger(__name__)


class SupabaseClient:
    """Supabase database client for ARIA claims processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client: Client = create_client(
            settings.database.supabase_url,
            settings.database.supabase_anon_key
        )
        self.service_client: Client = create_client(
            settings.database.supabase_url,
            settings.database.supabase_service_role_key
        )
    
    async def health_check(self) -> bool:
        """Check database connection health"""
        try:
            result = self.client.table('claims').select('id').limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def initialize_schema(self):
        """Initialize database schema"""
        try:
            # Read and execute schema file
            schema_file = self.settings.database.schema_file
            if schema_file and schema_file.exists():
                with open(schema_file, 'r') as f:
                    schema_sql = f.read()
                
                # Execute schema using direct SQL
                # Note: This would need to be done via Supabase SQL editor or API
                logger.info("Schema file found. Please execute the schema manually in Supabase SQL editor.")
                logger.info(f"Schema file location: {schema_file}")
                return True
            else:
                logger.warning("Schema file not found. Creating basic tables...")
                await self._create_basic_tables()
                return True
                
        except Exception as e:
            logger.error(f"Error initializing schema: {e}")
            return False
    
    async def _create_basic_tables(self):
        """Create basic tables if schema file not available"""
        # This is a simplified version - the full schema should be applied via SQL editor
        logger.info("Creating basic claims table structure...")
        # In a real implementation, we'd use Supabase's table creation API
        # For now, we'll assume the schema is applied manually
    
    async def create_claim(self, claim_data: Dict) -> Dict:
        """Create a new claim record"""
        try:
            # Generate claim number if not provided
            if 'claim_number' not in claim_data:
                claim_data['claim_number'] = await self._generate_claim_number()
            
            # Add timestamps
            claim_data['created_at'] = datetime.now().isoformat()
            claim_data['updated_at'] = datetime.now().isoformat()
            
            # Insert claim
            result = self.service_client.table('claims').insert(claim_data).execute()
            
            if result.data:
                logger.info(f"Created claim: {result.data[0]['claim_number']}")
                return result.data[0]
            else:
                raise Exception("No data returned from insert")
                
        except Exception as e:
            logger.error(f"Error creating claim: {e}")
            raise
    
    async def get_claim(self, claim_id: str) -> Optional[Dict]:
        """Get claim by ID"""
        try:
            result = self.client.table('claims').select('*').eq('id', claim_id).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error getting claim {claim_id}: {e}")
            return None
    
    async def get_claim_by_number(self, claim_number: str) -> Optional[Dict]:
        """Get claim by claim number"""
        try:
            result = self.client.table('claims').select('*').eq('claim_number', claim_number).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error getting claim by number {claim_number}: {e}")
            return None
    
    async def update_claim(self, claim_id: str, updates: Dict) -> bool:
        """Update claim record"""
        try:
            updates['updated_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('claims').update(updates).eq('id', claim_id).execute()
            
            if result.data:
                logger.info(f"Updated claim {claim_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error updating claim {claim_id}: {e}")
            return False
    
    async def update_claim_status(self, claim_id: str, status: str, message: str) -> bool:
        """Update claim status and create timeline entry"""
        try:
            # Update claim status
            await self.update_claim(claim_id, {'status': status})
            
            # Create timeline entry
            timeline_entry = {
                'claim_id': claim_id,
                'event_type': 'status_updated',
                'title': f'Status changed to {status}',
                'description': message,
                'actor_type': 'system',
                'created_at': datetime.now().isoformat()
            }
            
            await self.create_timeline_entry(timeline_entry)
            return True
            
        except Exception as e:
            logger.error(f"Error updating claim status: {e}")
            return False
    
    async def create_timeline_entry(self, timeline_data: Dict) -> bool:
        """Create timeline entry"""
        try:
            result = self.service_client.table('claim_timeline').insert(timeline_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error creating timeline entry: {e}")
            return False
    
    async def get_claim_timeline(self, claim_id: str) -> List[Dict]:
        """Get claim timeline"""
        try:
            result = self.client.table('claim_timeline').select('*').eq('claim_id', claim_id).order('created_at').execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting timeline for claim {claim_id}: {e}")
            return []
    
    async def store_claim_document(self, claim_id: str, document_data: Dict) -> bool:
        """Store claim document record"""
        try:
            document_data['claim_id'] = claim_id
            document_data['uploaded_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('claim_documents').insert(document_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error storing document: {e}")
            return False
    
    async def get_claim_documents(self, claim_id: str) -> List[Dict]:
        """Get documents for claim"""
        try:
            result = self.client.table('claim_documents').select('*').eq('claim_id', claim_id).execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting documents for claim {claim_id}: {e}")
            return []
    
    async def update_document_ocr(self, document_id: str, ocr_result: Dict) -> bool:
        """Update document with OCR results"""
        try:
            updates = {
                'ocr_text': ocr_result.get('text', ''),
                'ocr_confidence': ocr_result.get('confidence', 0.0),
                'extracted_data': ocr_result.get('entities', {}),
                'is_processed': True,
                'processed_at': datetime.now().isoformat()
            }
            
            result = self.service_client.table('claim_documents').update(updates).eq('id', document_id).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error updating document OCR: {e}")
            return False
    
    async def store_ai_analysis(self, claim_id: str, analysis_results: Dict) -> bool:
        """Store AI analysis results"""
        try:
            analysis_data = {
                'claim_id': claim_id,
                'analysis_type': 'comprehensive',
                'model_version': 'gpt-4-turbo',
                'results': analysis_results,
                'confidence_score': analysis_results.get('overall', {}).get('overall_confidence', 0.0),
                'created_at': datetime.now().isoformat()
            }
            
            result = self.service_client.table('ai_analysis').insert(analysis_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error storing AI analysis: {e}")
            return False
    
    async def assign_claim(self, claim_id: str, agent_email: str) -> bool:
        """Assign claim to agent"""
        try:
            # Get or create agent
            agent = await self._get_or_create_agent(agent_email)
            
            if agent:
                updates = {
                    'assigned_agent_id': agent['id'],
                    'status': 'human_review'
                }
                return await self.update_claim(claim_id, updates)
            
            return False
            
        except Exception as e:
            logger.error(f"Error assigning claim: {e}")
            return False
    
    async def store_agent_decision(self, claim_id: str, decision_data: Dict) -> bool:
        """Store agent decision"""
        try:
            decision_data['claim_id'] = claim_id
            decision_data['created_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('agent_decisions').insert(decision_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error storing agent decision: {e}")
            return False
    
    async def log_notification(self, notification_data: Dict) -> bool:
        """Log notification"""
        try:
            notification_data['created_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('notifications').insert(notification_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error logging notification: {e}")
            return False
    
    async def find_best_agent(self, claim_type: str = None, priority: int = 3) -> Optional[Dict]:
        """Find best available agent"""
        try:
            # Simple agent selection - in production this would be more sophisticated
            result = self.client.table('agents').select('*').eq('is_active', True).limit(1).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error finding agent: {e}")
            return None
    
    async def get_stuck_claims(self) -> List[Dict]:
        """Get claims that are stuck in processing"""
        try:
            # This would implement logic to find stuck claims
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Error getting stuck claims: {e}")
            return []
    
    async def get_sla_breaches(self) -> List[Dict]:
        """Get claims with SLA breaches"""
        try:
            # This would implement SLA breach detection
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Error getting SLA breaches: {e}")
            return []
    
    async def _generate_claim_number(self) -> str:
        """Generate unique claim number"""
        try:
            # Get current year
            year = datetime.now().year
            
            # Count claims for this year
            result = self.client.table('claims').select('id', count='exact').execute()
            count = result.count or 0
            
            # Generate claim number
            claim_number = f"CLAIM-{year}-{count + 1:06d}"
            return claim_number
            
        except Exception as e:
            logger.error(f"Error generating claim number: {e}")
            return f"CLAIM-{datetime.now().year}-{uuid.uuid4().hex[:6].upper()}"
    
    async def _get_or_create_agent(self, agent_email: str) -> Optional[Dict]:
        """Get or create agent record"""
        try:
            # Try to get existing agent
            result = self.client.table('agents').select('*').eq('email', agent_email).execute()
            
            if result.data:
                return result.data[0]
            
            # Create new agent
            agent_data = {
                'email': agent_email,
                'name': agent_email.split('@')[0].replace('.', ' ').title(),
                'team': 'Claims Processing',
                'role': 'Agent',
                'is_active': True,
                'created_at': datetime.now().isoformat()
            }
            
            result = self.service_client.table('agents').insert(agent_data).execute()
            
            if result.data:
                return result.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting/creating agent: {e}")
            return None
