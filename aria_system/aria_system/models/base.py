"""
Base database models and mixins for ARIA system.
"""

import uuid
from datetime import datetime
from typing import Any

from sqlalchemy import Column, DateTime, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func


Base = declarative_base()


class UUIDMixin:
    """Mixin for models that use UUID as primary key."""
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        unique=True,
        nullable=False,
        index=True
    )


class TimestampMixin:
    """Mixin for models that need created_at and updated_at timestamps."""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        index=True
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True
    )


class SoftDeleteMixin:
    """Mixin for models that support soft deletion."""
    
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        index=True
    )
    
    def soft_delete(self):
        """Mark the record as deleted."""
        self.deleted_at = datetime.utcnow()
    
    def restore(self):
        """Restore a soft-deleted record."""
        self.deleted_at = None
    
    @property
    def is_deleted(self) -> bool:
        """Check if the record is soft-deleted."""
        return self.deleted_at is not None


class AuditMixin:
    """Mixin for models that need audit trail information."""
    
    created_by = Column(String(255), nullable=True)
    updated_by = Column(String(255), nullable=True)
    
    # JSON field for storing additional metadata
    metadata = Column(Text, nullable=True)  # Store as JSON string
    
    def set_metadata(self, data: dict):
        """Set metadata as JSON string."""
        import json
        self.metadata = json.dumps(data) if data else None
    
    def get_metadata(self) -> dict:
        """Get metadata as dictionary."""
        import json
        return json.loads(self.metadata) if self.metadata else {}


class BaseModel(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin, AuditMixin):
    """Base model with all common mixins."""
    
    __abstract__ = True
    
    def to_dict(self) -> dict[str, Any]:
        """Convert model instance to dictionary."""
        result = {}
        for column in self.__table__.columns:
            value = getattr(self, column.name)
            if isinstance(value, datetime):
                value = value.isoformat()
            elif isinstance(value, uuid.UUID):
                value = str(value)
            result[column.name] = value
        return result
    
    def update_from_dict(self, data: dict[str, Any]):
        """Update model instance from dictionary."""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of the model."""
        return f"<{self.__class__.__name__}(id={self.id})>"
