"""
Audit log database models for ARIA system.
"""

import enum
from typing import Optional

from sqlalchemy import Column, String, Text, Enum, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .base import BaseModel


class AuditAction(str, enum.Enum):
    """Enumeration for audit actions."""
    
    # Claim actions
    CLAIM_CREATED = "claim_created"
    CLAIM_UPDATED = "claim_updated"
    CLAIM_STATUS_CHANGED = "claim_status_changed"
    CLAIM_ASSIGNED = "claim_assigned"
    CLAIM_APPROVED = "claim_approved"
    CLAIM_DENIED = "claim_denied"
    CLAIM_SETTLED = "claim_settled"
    CLAIM_CLOSED = "claim_closed"
    
    # Document actions
    DOCUMENT_UPLOADED = "document_uploaded"
    DOCUMENT_PROCESSED = "document_processed"
    DOCUMENT_VALIDATED = "document_validated"
    DOCUMENT_REJECTED = "document_rejected"
    DOCUMENT_DELETED = "document_deleted"
    
    # AI actions
    AI_ANALYSIS_STARTED = "ai_analysis_started"
    AI_ANALYSIS_COMPLETED = "ai_analysis_completed"
    AI_DECISION_MADE = "ai_decision_made"
    AI_CONFIDENCE_CALCULATED = "ai_confidence_calculated"
    
    # Human interaction actions
    HUMAN_REVIEW_REQUESTED = "human_review_requested"
    HUMAN_RESPONSE_RECEIVED = "human_response_received"
    EXPERT_ASSIGNED = "expert_assigned"
    INTERACTION_ESCALATED = "interaction_escalated"
    APPROVAL_GRANTED = "approval_granted"
    APPROVAL_DENIED = "approval_denied"
    
    # Communication actions
    EMAIL_SENT = "email_sent"
    EMAIL_RECEIVED = "email_received"
    NOTIFICATION_SENT = "notification_sent"
    ACKNOWLEDGMENT_SENT = "acknowledgment_sent"
    DECISION_COMMUNICATED = "decision_communicated"
    
    # System actions
    SYSTEM_ERROR = "system_error"
    SYSTEM_WARNING = "system_warning"
    BACKUP_CREATED = "backup_created"
    DATA_EXPORTED = "data_exported"
    CONFIGURATION_CHANGED = "configuration_changed"
    
    # Security actions
    ACCESS_GRANTED = "access_granted"
    ACCESS_DENIED = "access_denied"
    LOGIN_ATTEMPT = "login_attempt"
    PERMISSION_CHANGED = "permission_changed"
    SECURITY_VIOLATION = "security_violation"


class AuditLog(BaseModel):
    """Comprehensive audit logging for all system actions."""
    
    __tablename__ = "audit_logs"
    
    # Relationship to claim (optional - some actions may not be claim-specific)
    claim_id = Column(UUID(as_uuid=True), ForeignKey("claims.id"), nullable=True, index=True)
    
    # Action details
    action = Column(Enum(AuditAction), nullable=False, index=True)
    entity_type = Column(String(50), nullable=False, index=True)  # claim, document, interaction, etc.
    entity_id = Column(String(255), nullable=True, index=True)    # ID of the affected entity
    
    # Actor information
    actor_type = Column(String(50), nullable=False)  # user, system, ai, external
    actor_id = Column(String(255), nullable=True)    # user ID, system component, etc.
    actor_name = Column(String(255), nullable=True)  # human-readable actor name
    
    # Action context
    description = Column(Text, nullable=False)
    details = Column(Text, nullable=True)  # JSON string with additional details
    
    # Request/Response information
    request_data = Column(Text, nullable=True)   # JSON string
    response_data = Column(Text, nullable=True)  # JSON string
    
    # Technical details
    ip_address = Column(String(45), nullable=True)  # IPv4 or IPv6
    user_agent = Column(String(500), nullable=True)
    session_id = Column(String(255), nullable=True)
    correlation_id = Column(String(255), nullable=True, index=True)  # For tracing related actions
    
    # Outcome
    success = Column(String(10), nullable=False, default="true")  # "true", "false", "partial"
    error_message = Column(Text, nullable=True)
    error_code = Column(String(50), nullable=True)
    
    # Performance metrics
    duration_ms = Column(String(20), nullable=True)  # Processing duration in milliseconds
    
    # Compliance and retention
    retention_period = Column(String(20), nullable=True)  # e.g., "7_years", "permanent"
    compliance_tags = Column(Text, nullable=True)  # JSON array of compliance requirements
    
    # Relationships
    claim = relationship("Claim", back_populates="audit_logs")
    
    def __repr__(self) -> str:
        return f"<AuditLog(action={self.action}, entity_type={self.entity_type})>"
    
    @classmethod
    def create_log(
        cls,
        action: AuditAction,
        entity_type: str,
        description: str,
        actor_type: str = "system",
        actor_id: Optional[str] = None,
        actor_name: Optional[str] = None,
        entity_id: Optional[str] = None,
        claim_id: Optional[str] = None,
        details: Optional[dict] = None,
        request_data: Optional[dict] = None,
        response_data: Optional[dict] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        error_code: Optional[str] = None,
        duration_ms: Optional[int] = None,
        correlation_id: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        session_id: Optional[str] = None,
    ) -> "AuditLog":
        """Create a new audit log entry."""
        import json
        
        log_entry = cls(
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            claim_id=claim_id,
            actor_type=actor_type,
            actor_id=actor_id,
            actor_name=actor_name,
            description=description,
            success="true" if success else "false",
            error_message=error_message,
            error_code=error_code,
            correlation_id=correlation_id,
            ip_address=ip_address,
            user_agent=user_agent,
            session_id=session_id,
        )
        
        # Set JSON fields
        if details:
            log_entry.details = json.dumps(details)
        if request_data:
            log_entry.request_data = json.dumps(request_data)
        if response_data:
            log_entry.response_data = json.dumps(response_data)
        if duration_ms is not None:
            log_entry.duration_ms = str(duration_ms)
        
        return log_entry
    
    def set_details(self, details: dict):
        """Set details as JSON string."""
        import json
        self.details = json.dumps(details) if details else None
    
    def get_details(self) -> dict:
        """Get details as dictionary."""
        import json
        return json.loads(self.details) if self.details else {}
    
    def set_request_data(self, data: dict):
        """Set request data as JSON string."""
        import json
        self.request_data = json.dumps(data) if data else None
    
    def get_request_data(self) -> dict:
        """Get request data as dictionary."""
        import json
        return json.loads(self.request_data) if self.request_data else {}
    
    def set_response_data(self, data: dict):
        """Set response data as JSON string."""
        import json
        self.response_data = json.dumps(data) if data else None
    
    def get_response_data(self) -> dict:
        """Get response data as dictionary."""
        import json
        return json.loads(self.response_data) if self.response_data else {}
    
    def set_compliance_tags(self, tags: list):
        """Set compliance tags as JSON string."""
        import json
        self.compliance_tags = json.dumps(tags) if tags else None
    
    def get_compliance_tags(self) -> list:
        """Get compliance tags as list."""
        import json
        return json.loads(self.compliance_tags) if self.compliance_tags else []
    
    @property
    def is_successful(self) -> bool:
        """Check if the action was successful."""
        return self.success == "true"
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get duration in seconds."""
        if self.duration_ms:
            try:
                return float(self.duration_ms) / 1000
            except ValueError:
                return None
        return None
