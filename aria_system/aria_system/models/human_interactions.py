"""
Human interaction database models for ARIA system.
"""

import enum
from typing import Optional

from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .base import BaseModel


class ExpertType(str, enum.Enum):
    """Enumeration for expert types."""
    
    CLAIMS_REVIEWER = "claims_reviewer"
    SENIOR_ADJUSTER = "senior_adjuster"
    FRAUD_INVESTIGATOR = "fraud_investigator"
    MANAGER_APPROVER = "manager_approver"
    CUSTOMER_SERVICE = "customer_service"
    LEGAL_COUNSEL = "legal_counsel"
    MEDICAL_EXPERT = "medical_expert"
    TECHNICAL_EXPERT = "technical_expert"


class InteractionStatus(str, enum.Enum):
    """Enumeration for interaction status."""
    
    PENDING = "pending"
    SENT = "sent"
    ACKNOWLEDGED = "acknowledged"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    FAILED = "failed"


class InteractionChannel(str, enum.Enum):
    """Enumeration for communication channels."""
    
    SLACK = "slack"
    EMAIL = "email"
    WHATSAPP = "whatsapp"
    SMS = "sms"
    PHONE = "phone"
    WEB_PORTAL = "web_portal"


class UrgencyLevel(str, enum.Enum):
    """Enumeration for urgency levels."""
    
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"
    CRITICAL = "critical"


class HumanInteraction(BaseModel):
    """Human interaction tracking for claims processing."""
    
    __tablename__ = "human_interactions"
    
    # Relationship to claim
    claim_id = Column(UUID(as_uuid=True), ForeignKey("claims.id"), nullable=False, index=True)
    
    # Interaction details
    interaction_id = Column(String(100), unique=True, nullable=False, index=True)
    expert_type = Column(Enum(ExpertType), nullable=False, index=True)
    status = Column(Enum(InteractionStatus), default=InteractionStatus.PENDING, nullable=False, index=True)
    
    # Communication details
    channel = Column(Enum(InteractionChannel), nullable=False)
    recipient = Column(String(255), nullable=False)  # email, phone, slack user ID
    urgency = Column(Enum(UrgencyLevel), default=UrgencyLevel.NORMAL, nullable=False)
    
    # Request information
    request_subject = Column(String(500), nullable=True)
    request_text = Column(Text, nullable=False)
    request_context = Column(Text, nullable=True)  # JSON string
    
    # Response information
    response_text = Column(Text, nullable=True)
    response_data = Column(Text, nullable=True)  # JSON string for structured responses
    response_received_at = Column(String(50), nullable=True)  # ISO datetime string
    
    # Processing information
    sent_at = Column(String(50), nullable=True)      # ISO datetime string
    acknowledged_at = Column(String(50), nullable=True)  # ISO datetime string
    completed_at = Column(String(50), nullable=True)    # ISO datetime string
    
    # Timing and SLA
    expected_response_time = Column(Integer, nullable=True)  # minutes
    actual_response_time = Column(Integer, nullable=True)   # minutes
    sla_met = Column(Boolean, nullable=True)
    
    # Follow-up and escalation
    requires_follow_up = Column(Boolean, default=False, nullable=False)
    follow_up_at = Column(String(50), nullable=True)  # ISO datetime string
    escalated = Column(Boolean, default=False, nullable=False)
    escalated_to = Column(String(255), nullable=True)
    escalation_reason = Column(Text, nullable=True)
    
    # Quality and feedback
    satisfaction_rating = Column(Integer, nullable=True)  # 1-5 scale
    feedback = Column(Text, nullable=True)
    
    # Technical details
    humanlayer_run_id = Column(String(255), nullable=True)
    humanlayer_contact_id = Column(String(255), nullable=True)
    external_reference = Column(String(255), nullable=True)
    
    # Relationships
    claim = relationship("Claim", back_populates="human_interactions")
    
    def __repr__(self) -> str:
        return f"<HumanInteraction(interaction_id={self.interaction_id}, expert_type={self.expert_type})>"
    
    @property
    def is_overdue(self) -> bool:
        """Check if interaction is overdue based on expected response time."""
        if not self.expected_response_time or self.status in [InteractionStatus.COMPLETED, InteractionStatus.CANCELLED]:
            return False
        
        try:
            from datetime import datetime, timedelta
            sent_time = datetime.fromisoformat(self.sent_at.replace('Z', '+00:00')) if self.sent_at else self.created_at
            expected_completion = sent_time + timedelta(minutes=self.expected_response_time)
            return datetime.utcnow() > expected_completion
        except (ValueError, AttributeError):
            return False
    
    @property
    def response_time_minutes(self) -> Optional[int]:
        """Calculate actual response time in minutes."""
        if not self.sent_at or not self.completed_at:
            return None
        
        try:
            from datetime import datetime
            sent = datetime.fromisoformat(self.sent_at.replace('Z', '+00:00'))
            completed = datetime.fromisoformat(self.completed_at.replace('Z', '+00:00'))
            return int((completed - sent).total_seconds() / 60)
        except (ValueError, AttributeError):
            return None
    
    def set_request_context(self, context: dict):
        """Set request context as JSON string."""
        import json
        self.request_context = json.dumps(context) if context else None
    
    def get_request_context(self) -> dict:
        """Get request context as dictionary."""
        import json
        return json.loads(self.request_context) if self.request_context else {}
    
    def set_response_data(self, data: dict):
        """Set response data as JSON string."""
        import json
        self.response_data = json.dumps(data) if data else None
    
    def get_response_data(self) -> dict:
        """Get response data as dictionary."""
        import json
        return json.loads(self.response_data) if self.response_data else {}
    
    def update_status(self, new_status: InteractionStatus, response_text: Optional[str] = None):
        """Update interaction status with timestamp."""
        from datetime import datetime
        
        old_status = self.status
        self.status = new_status
        current_time = datetime.utcnow().isoformat()
        
        if new_status == InteractionStatus.SENT:
            self.sent_at = current_time
        elif new_status == InteractionStatus.ACKNOWLEDGED:
            self.acknowledged_at = current_time
        elif new_status == InteractionStatus.COMPLETED:
            self.completed_at = current_time
            if response_text:
                self.response_text = response_text
            
            # Calculate response time and SLA compliance
            if self.sent_at:
                self.actual_response_time = self.response_time_minutes
                if self.expected_response_time:
                    self.sla_met = (self.actual_response_time or 0) <= self.expected_response_time
        
        # Log status change
        from ..utils.logger import log_human_interaction
        log_human_interaction(
            self.interaction_id,
            self.expert_type.value,
            f"Status changed from {old_status} to {new_status}",
            {"old_status": old_status, "new_status": new_status}
        )
    
    def escalate(self, escalated_to: str, reason: str):
        """Escalate the interaction to another expert."""
        self.escalated = True
        self.escalated_to = escalated_to
        self.escalation_reason = reason
        
        # Log escalation
        from ..utils.logger import log_human_interaction
        log_human_interaction(
            self.interaction_id,
            self.expert_type.value,
            f"Escalated to {escalated_to}",
            {"escalated_to": escalated_to, "reason": reason}
        )
    
    def set_follow_up(self, follow_up_time: str):
        """Set follow-up time for the interaction."""
        self.requires_follow_up = True
        self.follow_up_at = follow_up_time
    
    def get_expected_response_time_by_urgency(self) -> int:
        """Get expected response time based on urgency level."""
        urgency_sla = {
            UrgencyLevel.LOW: 480,      # 8 hours
            UrgencyLevel.NORMAL: 240,   # 4 hours
            UrgencyLevel.HIGH: 60,      # 1 hour
            UrgencyLevel.URGENT: 30,    # 30 minutes
            UrgencyLevel.CRITICAL: 15,  # 15 minutes
        }
        return urgency_sla.get(self.urgency, 240)
