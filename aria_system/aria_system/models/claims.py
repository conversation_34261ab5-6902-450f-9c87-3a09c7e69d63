"""
Claims database models for ARIA system.
"""

import enum
from datetime import datetime
from typing import Optional

from sqlalchemy import Column, String, Text, Float, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Key
from sqlalchemy.orm import relationship

from .base import BaseModel


class ClaimStatus(str, enum.Enum):
    """Enumeration for claim status values."""
    
    RECEIVED = "received"
    PROCESSING = "processing"
    DOCUMENT_REVIEW = "document_review"
    AI_ANALYSIS = "ai_analysis"
    HUMAN_REVIEW = "human_review"
    APPROVED = "approved"
    DENIED = "denied"
    REQUIRES_INVESTIGATION = "requires_investigation"
    SETTLED = "settled"
    CLOSED = "closed"
    ERROR = "error"


class ClaimType(str, enum.Enum):
    """Enumeration for claim types."""
    
    AUTO_LIABILITY = "auto_liability"
    PROPERTY_DAMAGE = "property_damage"
    BODILY_INJURY = "bodily_injury"
    GENERAL_LIABILITY = "general_liability"
    PROFESSIONAL_LIABILITY = "professional_liability"
    PRODUCT_LIABILITY = "product_liability"
    CYBER_LIABILITY = "cyber_liability"
    OTHER = "other"


class CoverageDecision(str, enum.Enum):
    """Enumeration for coverage decisions."""
    
    COVERED = "covered"
    NOT_COVERED = "not_covered"
    PARTIALLY_COVERED = "partially_covered"
    REQUIRES_INVESTIGATION = "requires_investigation"
    PENDING = "pending"


class Claim(BaseModel):
    """Main claims table storing all claim information."""
    
    __tablename__ = "claims"
    
    # Basic claim information
    claim_number = Column(String(50), unique=True, nullable=False, index=True)
    claimant_name = Column(String(255), nullable=False)
    claimant_email = Column(String(255), nullable=False, index=True)
    claimant_phone = Column(String(50), nullable=True)
    
    # Claim details
    subject = Column(String(500), nullable=True)
    description = Column(Text, nullable=False)
    incident_date = Column(String(100), nullable=True)  # Extracted from description
    incident_location = Column(String(500), nullable=True)
    
    # Classification
    claim_type = Column(Enum(ClaimType), nullable=True, index=True)
    status = Column(Enum(ClaimStatus), default=ClaimStatus.RECEIVED, nullable=False, index=True)
    priority = Column(String(20), default="normal", nullable=False)  # low, normal, high, urgent
    
    # Policy information
    policy_number = Column(String(100), nullable=True, index=True)
    policy_holder = Column(String(255), nullable=True)
    
    # Financial information
    claimed_amount = Column(Float, nullable=True)
    estimated_value = Column(Float, nullable=True)
    settlement_amount = Column(Float, nullable=True)
    deductible = Column(Float, nullable=True)
    
    # AI Analysis results
    coverage_decision = Column(Enum(CoverageDecision), nullable=True)
    coverage_rationale = Column(Text, nullable=True)
    fault_percentage = Column(Float, nullable=True)
    confidence_score = Column(Float, nullable=True)
    complexity_level = Column(String(20), nullable=True)  # low, medium, high
    
    # Processing information
    requires_human_review = Column(Boolean, default=False, nullable=False)
    auto_approved = Column(Boolean, default=False, nullable=False)
    processing_start = Column(String(50), nullable=True)  # ISO datetime string
    processing_end = Column(String(50), nullable=True)    # ISO datetime string
    
    # Risk assessment
    fraud_indicators = Column(Text, nullable=True)  # JSON string
    risk_factors = Column(Text, nullable=True)      # JSON string
    
    # Communication tracking
    acknowledgment_sent = Column(Boolean, default=False, nullable=False)
    decision_sent = Column(Boolean, default=False, nullable=False)
    last_communication = Column(String(50), nullable=True)  # ISO datetime string
    
    # Relationships
    documents = relationship("Document", back_populates="claim", cascade="all, delete-orphan")
    human_interactions = relationship("HumanInteraction", back_populates="claim", cascade="all, delete-orphan")
    audit_logs = relationship("AuditLog", back_populates="claim", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Claim(claim_number={self.claim_number}, status={self.status})>"
    
    @property
    def processing_time_minutes(self) -> Optional[float]:
        """Calculate processing time in minutes."""
        if not self.processing_start or not self.processing_end:
            return None
        
        try:
            from datetime import datetime
            start = datetime.fromisoformat(self.processing_start.replace('Z', '+00:00'))
            end = datetime.fromisoformat(self.processing_end.replace('Z', '+00:00'))
            return (end - start).total_seconds() / 60
        except (ValueError, AttributeError):
            return None
    
    @property
    def is_high_value(self) -> bool:
        """Check if this is a high-value claim."""
        return (self.estimated_value or 0) > 50000 or (self.claimed_amount or 0) > 50000
    
    @property
    def is_complex(self) -> bool:
        """Check if this is a complex claim."""
        return (
            self.complexity_level == "high" or
            (self.confidence_score or 1.0) < 0.7 or
            self.requires_human_review or
            self.is_high_value
        )
    
    def set_fraud_indicators(self, indicators: list):
        """Set fraud indicators as JSON string."""
        import json
        self.fraud_indicators = json.dumps(indicators) if indicators else None
    
    def get_fraud_indicators(self) -> list:
        """Get fraud indicators as list."""
        import json
        return json.loads(self.fraud_indicators) if self.fraud_indicators else []
    
    def set_risk_factors(self, factors: list):
        """Set risk factors as JSON string."""
        import json
        self.risk_factors = json.dumps(factors) if factors else None
    
    def get_risk_factors(self) -> list:
        """Get risk factors as list."""
        import json
        return json.loads(self.risk_factors) if self.risk_factors else []
    
    def update_status(self, new_status: ClaimStatus, updated_by: Optional[str] = None):
        """Update claim status with audit trail."""
        old_status = self.status
        self.status = new_status
        self.updated_by = updated_by
        
        # Log status change
        from ..utils.logger import log_claim_event
        log_claim_event(
            self.claim_number,
            f"Status changed from {old_status} to {new_status}",
            {"old_status": old_status, "new_status": new_status, "updated_by": updated_by}
        )
