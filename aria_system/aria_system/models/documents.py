"""
Document database models for ARIA system.
"""

import enum
from typing import Optional

from sqlalchemy import Column, String, Text, Inte<PERSON>, <PERSON>olean, <PERSON>um, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from .base import BaseModel


class DocumentType(str, enum.Enum):
    """Enumeration for document types."""
    
    INCIDENT_REPORT = "incident_report"
    POLICE_REPORT = "police_report"
    MEDICAL_REPORT = "medical_report"
    REPAIR_ESTIMATE = "repair_estimate"
    INVOICE = "invoice"
    RECEIPT = "receipt"
    PHOTO = "photo"
    VIDEO = "video"
    POLICY_DOCUMENT = "policy_document"
    CERTIFICATE = "certificate"
    CORRESPONDENCE = "correspondence"
    WITNESS_STATEMENT = "witness_statement"
    EXPERT_REPORT = "expert_report"
    LEGAL_DOCUMENT = "legal_document"
    OTHER = "other"


class DocumentStatus(str, enum.Enum):
    """Enumeration for document processing status."""
    
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    FAILED = "failed"
    VALIDATED = "validated"
    REJECTED = "rejected"


class Document(BaseModel):
    """Document storage and processing information."""
    
    __tablename__ = "documents"
    
    # Relationship to claim
    claim_id = Column(UUID(as_uuid=True), ForeignKey("claims.id"), nullable=False, index=True)
    
    # File information
    filename = Column(String(255), nullable=False)
    original_filename = Column(String(255), nullable=False)
    file_extension = Column(String(10), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=True)
    
    # Document classification
    document_type = Column(Enum(DocumentType), nullable=True, index=True)
    status = Column(Enum(DocumentStatus), default=DocumentStatus.UPLOADED, nullable=False, index=True)
    
    # Storage information
    storage_path = Column(String(500), nullable=True)
    cloud_urls = Column(Text, nullable=True)  # JSON string of cloud storage URLs
    
    # Processing results
    extracted_text = Column(Text, nullable=True)
    ocr_confidence = Column(String(10), nullable=True)  # Percentage as string
    processing_provider = Column(String(50), nullable=True)  # azure, aws, gcp
    processing_error = Column(Text, nullable=True)
    
    # Content analysis
    key_entities = Column(Text, nullable=True)     # JSON string
    important_dates = Column(Text, nullable=True)  # JSON string
    monetary_amounts = Column(Text, nullable=True) # JSON string
    
    # Validation
    is_valid = Column(Boolean, default=True, nullable=False)
    validation_errors = Column(Text, nullable=True)  # JSON string
    requires_manual_review = Column(Boolean, default=False, nullable=False)
    
    # Security
    is_sensitive = Column(Boolean, default=False, nullable=False)
    access_level = Column(String(20), default="standard", nullable=False)  # standard, restricted, confidential
    
    # Relationships
    claim = relationship("Claim", back_populates="documents")
    
    def __repr__(self) -> str:
        return f"<Document(filename={self.filename}, type={self.document_type})>"
    
    @property
    def file_size_mb(self) -> float:
        """Get file size in megabytes."""
        return self.file_size / (1024 * 1024) if self.file_size else 0
    
    @property
    def is_image(self) -> bool:
        """Check if document is an image."""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'}
        return self.file_extension.lower() in image_extensions
    
    @property
    def is_pdf(self) -> bool:
        """Check if document is a PDF."""
        return self.file_extension.lower() == '.pdf'
    
    @property
    def is_office_document(self) -> bool:
        """Check if document is an Office document."""
        office_extensions = {'.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx'}
        return self.file_extension.lower() in office_extensions
    
    def set_cloud_urls(self, urls: dict):
        """Set cloud storage URLs as JSON string."""
        import json
        self.cloud_urls = json.dumps(urls) if urls else None
    
    def get_cloud_urls(self) -> dict:
        """Get cloud storage URLs as dictionary."""
        import json
        return json.loads(self.cloud_urls) if self.cloud_urls else {}
    
    def set_key_entities(self, entities: list):
        """Set key entities as JSON string."""
        import json
        self.key_entities = json.dumps(entities) if entities else None
    
    def get_key_entities(self) -> list:
        """Get key entities as list."""
        import json
        return json.loads(self.key_entities) if self.key_entities else []
    
    def set_important_dates(self, dates: list):
        """Set important dates as JSON string."""
        import json
        self.important_dates = json.dumps(dates) if dates else None
    
    def get_important_dates(self) -> list:
        """Get important dates as list."""
        import json
        return json.loads(self.important_dates) if self.important_dates else []
    
    def set_monetary_amounts(self, amounts: list):
        """Set monetary amounts as JSON string."""
        import json
        self.monetary_amounts = json.dumps(amounts) if amounts else None
    
    def get_monetary_amounts(self) -> list:
        """Get monetary amounts as list."""
        import json
        return json.loads(self.monetary_amounts) if self.monetary_amounts else []
    
    def set_validation_errors(self, errors: list):
        """Set validation errors as JSON string."""
        import json
        self.validation_errors = json.dumps(errors) if errors else None
    
    def get_validation_errors(self) -> list:
        """Get validation errors as list."""
        import json
        return json.loads(self.validation_errors) if self.validation_errors else []
    
    def update_processing_status(self, status: DocumentStatus, provider: Optional[str] = None, error: Optional[str] = None):
        """Update document processing status."""
        old_status = self.status
        self.status = status
        
        if provider:
            self.processing_provider = provider
        
        if error:
            self.processing_error = error
        
        # Log status change
        from ..utils.logger import log_document_processing
        log_document_processing(
            str(self.id),
            self.filename,
            provider or "unknown",
            status == DocumentStatus.PROCESSED,
            {"old_status": old_status, "new_status": status, "error": error}
        )
    
    def classify_document_type(self) -> DocumentType:
        """Automatically classify document type based on filename and content."""
        filename_lower = self.filename.lower()
        
        # Classification based on filename patterns
        if any(keyword in filename_lower for keyword in ['police', 'incident', 'accident']):
            return DocumentType.INCIDENT_REPORT
        elif any(keyword in filename_lower for keyword in ['medical', 'doctor', 'hospital']):
            return DocumentType.MEDICAL_REPORT
        elif any(keyword in filename_lower for keyword in ['repair', 'estimate', 'quote']):
            return DocumentType.REPAIR_ESTIMATE
        elif any(keyword in filename_lower for keyword in ['invoice', 'bill']):
            return DocumentType.INVOICE
        elif any(keyword in filename_lower for keyword in ['receipt']):
            return DocumentType.RECEIPT
        elif any(keyword in filename_lower for keyword in ['policy', 'certificate']):
            return DocumentType.POLICY_DOCUMENT
        elif self.is_image:
            return DocumentType.PHOTO
        else:
            return DocumentType.OTHER
