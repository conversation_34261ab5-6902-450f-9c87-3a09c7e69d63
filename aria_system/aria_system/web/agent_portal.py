"""
ARIA Agent Review Portal
Dynamic web interface for human agents to review and approve claims
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from urllib.parse import quote

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from PIL import Image
import requests

from ..config import Settings
from ..database.supabase_client import SupabaseClient
from ..services.zendesk_service import ZendeskService
from ..services.notification_service import NotificationService

logger = logging.getLogger(__name__)


class AgentPortal:
    """Agent review portal for claim processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        self.zendesk = ZendeskService(settings)
        self.notifications = NotificationService(settings)
    
    def render_portal(self):
        """Render the main agent portal interface"""
        st.set_page_config(
            page_title="ARIA Agent Portal",
            page_icon="👥",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # Custom CSS
        st.markdown("""
        <style>
        .claim-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            background: white;
        }
        .priority-urgent { border-left: 4px solid #dc3545; }
        .priority-high { border-left: 4px solid #fd7e14; }
        .priority-normal { border-left: 4px solid #28a745; }
        .priority-low { border-left: 4px solid #6c757d; }
        
        .confidence-high { color: #28a745; font-weight: bold; }
        .confidence-medium { color: #fd7e14; font-weight: bold; }
        .confidence-low { color: #dc3545; font-weight: bold; }
        
        .action-button {
            margin: 4px;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .approve-btn { background: #28a745; color: white; }
        .deny-btn { background: #dc3545; color: white; }
        .info-btn { background: #17a2b8; color: white; }
        </style>
        """, unsafe_allow_html=True)
        
        # Sidebar navigation
        with st.sidebar:
            st.title("👥 ARIA Agent Portal")
            
            # Agent info
            agent_info = self._get_agent_info()
            if agent_info:
                st.success(f"Welcome, {agent_info['name']}")
                st.info(f"Active Claims: {agent_info['active_claims']}")
            
            # Navigation
            page = st.selectbox(
                "Navigate to:",
                ["Dashboard", "My Claims", "Review Queue", "Claim Details", "Analytics"]
            )
        
        # Main content
        if page == "Dashboard":
            self._render_dashboard()
        elif page == "My Claims":
            self._render_my_claims()
        elif page == "Review Queue":
            self._render_review_queue()
        elif page == "Claim Details":
            self._render_claim_details()
        elif page == "Analytics":
            self._render_analytics()
    
    def _render_dashboard(self):
        """Render agent dashboard"""
        st.title("📊 Agent Dashboard")
        
        # Metrics row
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Pending Reviews", "12", "3 urgent")
        with col2:
            st.metric("Completed Today", "8", "+2 vs yesterday")
        with col3:
            st.metric("Avg Review Time", "18 min", "-5 min vs target")
        with col4:
            st.metric("Approval Rate", "94%", "+2% vs last week")
        
        # Quick actions
        st.subheader("🚀 Quick Actions")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔍 Next Urgent Claim", use_container_width=True):
                st.session_state.review_claim_id = self._get_next_urgent_claim()
                st.rerun()
        
        with col2:
            if st.button("📋 My Queue", use_container_width=True):
                st.session_state.page = "My Claims"
                st.rerun()
        
        with col3:
            if st.button("📊 Performance", use_container_width=True):
                st.session_state.page = "Analytics"
                st.rerun()
        
        # Recent activity
        st.subheader("📈 Recent Activity")
        self._render_recent_activity()
    
    def _render_my_claims(self):
        """Render agent's assigned claims"""
        st.title("📋 My Claims")
        
        # Filters
        col1, col2, col3 = st.columns(3)
        with col1:
            status_filter = st.selectbox(
                "Status",
                ["All", "Pending Review", "In Progress", "Completed"]
            )
        with col2:
            priority_filter = st.selectbox(
                "Priority",
                ["All", "Urgent", "High", "Normal", "Low"]
            )
        with col3:
            sort_by = st.selectbox(
                "Sort by",
                ["Date Received", "Priority", "Amount", "Confidence"]
            )
        
        # Claims list
        claims = self._get_agent_claims(status_filter, priority_filter, sort_by)
        
        for claim in claims:
            self._render_claim_card(claim)
    
    def _render_review_queue(self):
        """Render claims review queue"""
        st.title("🔍 Review Queue")
        
        # Queue stats
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Total Queue", "47", "+5 today")
        with col2:
            st.metric("Urgent Items", "8", "2 overdue")
        with col3:
            st.metric("Avg Wait Time", "2.3 hrs", "-30 min vs target")
        
        # Queue list
        queue_claims = self._get_review_queue()
        
        for claim in queue_claims:
            with st.container():
                self._render_queue_item(claim)
    
    def _render_claim_details(self):
        """Render detailed claim review interface"""
        claim_id = st.query_params.get("claim_id") or st.session_state.get("review_claim_id")
        
        if not claim_id:
            st.warning("Please select a claim to review")
            return
        
        claim_data = self._get_claim_details(claim_id)
        if not claim_data:
            st.error("Claim not found")
            return
        
        st.title(f"🔍 Claim Review: {claim_data['claim_number']}")
        
        # Claim header
        self._render_claim_header(claim_data)
        
        # Main content tabs
        tab1, tab2, tab3, tab4, tab5 = st.tabs([
            "📄 Documents", "🤖 AI Analysis", "📋 Details", "💬 Timeline", "✅ Decision"
        ])
        
        with tab1:
            self._render_documents_tab(claim_data)
        
        with tab2:
            self._render_ai_analysis_tab(claim_data)
        
        with tab3:
            self._render_details_tab(claim_data)
        
        with tab4:
            self._render_timeline_tab(claim_data)
        
        with tab5:
            self._render_decision_tab(claim_data)
    
    def _render_claim_header(self, claim_data: Dict):
        """Render claim header with key information"""
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            priority_class = f"priority-{claim_data.get('priority_text', 'normal').lower()}"
            st.markdown(f"""
            <div class="claim-card {priority_class}">
                <h4>Priority: {claim_data.get('priority_text', 'Normal')}</h4>
                <p>Status: {claim_data.get('status', 'Unknown')}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            confidence = claim_data.get('ai_confidence_score', 0) * 100
            confidence_class = (
                "confidence-high" if confidence > 80 else
                "confidence-medium" if confidence > 60 else
                "confidence-low"
            )
            st.markdown(f"""
            <div class="claim-card">
                <h4>AI Confidence</h4>
                <p class="{confidence_class}">{confidence:.1f}%</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            st.markdown(f"""
            <div class="claim-card">
                <h4>Claimed Amount</h4>
                <p>${claim_data.get('claimed_amount', 'TBD'):,}</p>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            fraud_risk = claim_data.get('fraud_risk_score', 0) * 100
            st.markdown(f"""
            <div class="claim-card">
                <h4>Fraud Risk</h4>
                <p style="color: {'red' if fraud_risk > 50 else 'orange' if fraud_risk > 25 else 'green'}">{fraud_risk:.1f}%</p>
            </div>
            """, unsafe_allow_html=True)
    
    def _render_documents_tab(self, claim_data: Dict):
        """Render documents tab"""
        st.subheader("📄 Claim Documents")
        
        documents = self._get_claim_documents(claim_data['id'])
        
        if not documents:
            st.info("No documents uploaded yet")
            return
        
        for doc in documents:
            with st.expander(f"📎 {doc['filename']} ({doc['document_type']})"):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    # Display OCR text if available
                    if doc.get('ocr_text'):
                        st.text_area(
                            "Extracted Text",
                            doc['ocr_text'],
                            height=200,
                            key=f"ocr_{doc['id']}"
                        )
                    else:
                        st.info("OCR processing in progress...")
                
                with col2:
                    st.write(f"**Size:** {doc['file_size']:,} bytes")
                    st.write(f"**Type:** {doc['mime_type']}")
                    st.write(f"**Uploaded:** {doc['uploaded_at']}")
                    
                    if doc.get('ocr_confidence'):
                        st.write(f"**OCR Confidence:** {doc['ocr_confidence']*100:.1f}%")
                    
                    # Download button
                    if st.button(f"📥 Download", key=f"download_{doc['id']}"):
                        # Implement download functionality
                        st.success("Download started")
    
    def _render_ai_analysis_tab(self, claim_data: Dict):
        """Render AI analysis tab"""
        st.subheader("🤖 AI Analysis Results")
        
        analysis = self._get_ai_analysis(claim_data['id'])
        
        if not analysis:
            st.info("AI analysis in progress...")
            return
        
        # Classification results
        if 'classification' in analysis:
            classification = analysis['classification']
            
            col1, col2 = st.columns(2)
            with col1:
                st.write("**Claim Type:**", classification.get('claim_type', 'Unknown'))
                st.write("**Incident Type:**", classification.get('incident_type', 'Unknown'))
                st.write("**Severity:**", classification.get('severity', 'Unknown'))
            
            with col2:
                st.write("**Estimated Amount:**", f"${classification.get('estimated_amount', 0):,}")
                st.write("**Confidence:**", f"{classification.get('confidence_score', 0)*100:.1f}%")
            
            # Key facts
            if classification.get('key_facts'):
                st.write("**Key Facts:**")
                for fact in classification['key_facts']:
                    st.write(f"• {fact}")
        
        # Fraud analysis
        if 'fraud_analysis' in analysis:
            fraud = analysis['fraud_analysis']
            
            st.subheader("🚨 Fraud Analysis")
            
            risk_score = fraud.get('fraud_risk_score', 0) * 100
            risk_level = fraud.get('risk_level', 'unknown')
            
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Fraud Risk Score", f"{risk_score:.1f}%")
                st.write(f"**Risk Level:** {risk_level.title()}")
            
            with col2:
                if fraud.get('fraud_indicators'):
                    st.write("**Risk Indicators:**")
                    for indicator in fraud['fraud_indicators']:
                        st.write(f"⚠️ {indicator.get('indicator', 'Unknown')}")
        
        # Recommendations
        if 'overall' in analysis and analysis['overall'].get('recommendations'):
            st.subheader("💡 AI Recommendations")
            for rec in analysis['overall']['recommendations']:
                st.write(f"• {rec}")
    
    def _render_decision_tab(self, claim_data: Dict):
        """Render decision making tab"""
        st.subheader("✅ Make Decision")
        
        # Decision form
        with st.form("decision_form"):
            decision = st.radio(
                "Decision",
                ["Approve", "Deny", "Request More Information", "Escalate"],
                horizontal=True
            )
            
            if decision == "Approve":
                approved_amount = st.number_input(
                    "Approved Amount ($)",
                    min_value=0.0,
                    value=float(claim_data.get('claimed_amount', 0)),
                    step=100.0
                )
            elif decision == "Deny":
                denial_reason = st.selectbox(
                    "Denial Reason",
                    [
                        "Policy exclusion",
                        "Insufficient documentation",
                        "Fraud suspected",
                        "Coverage limit exceeded",
                        "Other"
                    ]
                )
            
            reasoning = st.text_area(
                "Reasoning/Comments",
                placeholder="Provide detailed reasoning for your decision...",
                height=150
            )
            
            confidence = st.slider(
                "Decision Confidence",
                1, 5, 4,
                help="How confident are you in this decision? (1=Low, 5=High)"
            )
            
            # Submit decision
            if st.form_submit_button("Submit Decision", type="primary"):
                decision_data = {
                    'decision': decision.lower().replace(' ', '_'),
                    'reasoning': reasoning,
                    'confidence': confidence,
                    'approved_amount': approved_amount if decision == "Approve" else None,
                    'denial_reason': denial_reason if decision == "Deny" else None
                }
                
                success = self._submit_decision(claim_data['id'], decision_data)
                
                if success:
                    st.success("✅ Decision submitted successfully!")
                    st.balloons()
                    
                    # Redirect to next claim
                    if st.button("➡️ Next Claim"):
                        next_claim = self._get_next_claim()
                        if next_claim:
                            st.session_state.review_claim_id = next_claim['id']
                            st.rerun()
                else:
                    st.error("❌ Failed to submit decision. Please try again.")
    
    def _get_agent_info(self) -> Optional[Dict]:
        """Get current agent information"""
        # This would typically get from session/auth
        return {
            'name': 'Demo Agent',
            'email': '<EMAIL>',
            'active_claims': 12
        }
    
    def _get_agent_claims(self, status_filter: str, priority_filter: str, sort_by: str) -> List[Dict]:
        """Get claims assigned to current agent"""
        # Mock data for demo
        return [
            {
                'id': '1',
                'claim_number': 'CLAIM-2024-001',
                'user_name': 'John Doe',
                'subject': 'Auto accident claim',
                'status': 'pending_review',
                'priority': 1,
                'priority_text': 'Urgent',
                'claimed_amount': 15000,
                'ai_confidence_score': 0.85,
                'created_at': datetime.now() - timedelta(hours=2)
            }
        ]
    
    def _get_claim_details(self, claim_id: str) -> Optional[Dict]:
        """Get detailed claim information"""
        # This would fetch from database
        return {
            'id': claim_id,
            'claim_number': 'CLAIM-2024-001',
            'user_name': 'John Doe',
            'user_email': '<EMAIL>',
            'subject': 'Auto accident claim',
            'description': 'Rear-end collision on Highway 101...',
            'status': 'pending_review',
            'priority': 1,
            'priority_text': 'Urgent',
            'claimed_amount': 15000,
            'ai_confidence_score': 0.85,
            'fraud_risk_score': 0.15,
            'created_at': datetime.now() - timedelta(hours=2)
        }
    
    def _submit_decision(self, claim_id: str, decision_data: Dict) -> bool:
        """Submit agent decision"""
        try:
            # This would update the database and trigger notifications
            logger.info(f"Decision submitted for claim {claim_id}: {decision_data}")
            return True
        except Exception as e:
            logger.error(f"Error submitting decision: {e}")
            return False
    
    def _render_claim_card(self, claim: Dict):
        """Render a claim card"""
        priority_class = f"priority-{claim.get('priority_text', 'normal').lower()}"
        
        st.markdown(f"""
        <div class="claim-card {priority_class}">
            <h4>{claim['claim_number']} - {claim['user_name']}</h4>
            <p><strong>Subject:</strong> {claim['subject']}</p>
            <p><strong>Amount:</strong> ${claim['claimed_amount']:,} | 
               <strong>Confidence:</strong> {claim['ai_confidence_score']*100:.1f}% | 
               <strong>Age:</strong> {(datetime.now() - claim['created_at']).total_seconds() / 3600:.1f}h</p>
        </div>
        """, unsafe_allow_html=True)
        
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("🔍 Review", key=f"review_{claim['id']}"):
                st.session_state.review_claim_id = claim['id']
                st.rerun()
        with col2:
            if st.button("📋 Zendesk", key=f"zendesk_{claim['id']}"):
                # Open Zendesk ticket
                pass
        with col3:
            if st.button("📞 Contact", key=f"contact_{claim['id']}"):
                # Contact customer
                pass
