"""
Multi-cloud services integration for ARIA system.
Handles document processing, storage, and email services across Azure, AWS, and GCP.
"""

import asyncio
import os
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import json

from ..config import settings
from ..utils.logger import get_logger, log_document_processing, log_email_event

logger = get_logger(__name__)


class CloudServicesManager:
    """Manages integrations with multiple cloud providers."""
    
    def __init__(self):
        self.azure_enabled = bool(settings.azure.storage_connection_string)
        self.aws_enabled = bool(settings.aws.access_key_id)
        self.gcp_enabled = bool(settings.gcp.application_credentials)
        
        self._azure_clients = {}
        self._aws_clients = {}
        self._gcp_clients = {}
        
        logger.info(f"Cloud services initialized - Azure: {self.azure_enabled}, AWS: {self.aws_enabled}, GCP: {self.gcp_enabled}")
    
    async def _get_azure_clients(self) -> Dict[str, Any]:
        """Initialize and get Azure clients."""
        if not self.azure_enabled:
            return {}
        
        if not self._azure_clients:
            try:
                from azure.storage.blob import BlobServiceClient
                from azure.cognitiveservices.vision.computervision import ComputerVisionClient
                from azure.cognitiveservices.vision.computervision.models import OperationStatusCodes
                from azure.communication.email import EmailClient
                from msrest.authentication import CognitiveServicesCredentials
                
                self._azure_clients = {
                    'blob': BlobServiceClient.from_connection_string(settings.azure.storage_connection_string),
                    'cv': ComputerVisionClient(
                        settings.azure.cv_endpoint,
                        CognitiveServicesCredentials(settings.azure.cv_key)
                    ) if settings.azure.cv_endpoint else None,
                    'email': EmailClient.from_connection_string(
                        settings.azure.communication_connection_string
                    ) if settings.azure.communication_connection_string else None
                }
                logger.info("Azure clients initialized")
            except ImportError as e:
                logger.error(f"Azure SDK not available: {e}")
                self.azure_enabled = False
        
        return self._azure_clients
    
    async def _get_aws_clients(self) -> Dict[str, Any]:
        """Initialize and get AWS clients."""
        if not self.aws_enabled:
            return {}
        
        if not self._aws_clients:
            try:
                import boto3
                
                session = boto3.Session(
                    aws_access_key_id=settings.aws.access_key_id,
                    aws_secret_access_key=settings.aws.secret_access_key,
                    region_name=settings.aws.region
                )
                
                self._aws_clients = {
                    'textract': session.client('textract'),
                    's3': session.client('s3'),
                    'ses': session.client('ses'),
                    'comprehend': session.client('comprehend')
                }
                logger.info("AWS clients initialized")
            except ImportError as e:
                logger.error(f"AWS SDK not available: {e}")
                self.aws_enabled = False
        
        return self._aws_clients
    
    async def _get_gcp_clients(self) -> Dict[str, Any]:
        """Initialize and get GCP clients."""
        if not self.gcp_enabled:
            return {}
        
        if not self._gcp_clients:
            try:
                from google.cloud import documentai
                from google.cloud import storage as gcs
                
                # Set credentials
                if settings.gcp.application_credentials:
                    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = settings.gcp.application_credentials
                
                self._gcp_clients = {
                    'document_ai': documentai.DocumentProcessorServiceClient(),
                    'storage': gcs.Client(project=settings.gcp.project_id)
                }
                logger.info("GCP clients initialized")
            except ImportError as e:
                logger.error(f"GCP SDK not available: {e}")
                self.gcp_enabled = False
        
        return self._gcp_clients
    
    async def store_document(self, file_content: bytes, filename: str, claim_id: str) -> Dict[str, str]:
        """Store document across multiple cloud providers for redundancy."""
        storage_results = {}
        full_path = f"claims/{claim_id}/{filename}"
        
        # Azure Blob Storage
        if self.azure_enabled:
            try:
                azure_clients = await self._get_azure_clients()
                blob_client = azure_clients['blob'].get_blob_client(
                    container="aria-documents",
                    blob=full_path
                )
                blob_client.upload_blob(file_content, overwrite=True)
                storage_results["azure"] = blob_client.url
                logger.info(f"Document stored in Azure: {filename}")
            except Exception as e:
                logger.error(f"Azure storage error for {filename}: {e}")
                storage_results["azure_error"] = str(e)
        
        # AWS S3
        if self.aws_enabled:
            try:
                aws_clients = await self._get_aws_clients()
                aws_clients['s3'].put_object(
                    Bucket=settings.aws.s3_bucket,
                    Key=full_path,
                    Body=file_content,
                    ContentType=self._get_content_type(filename)
                )
                storage_results["aws"] = f"s3://{settings.aws.s3_bucket}/{full_path}"
                logger.info(f"Document stored in AWS S3: {filename}")
            except Exception as e:
                logger.error(f"AWS S3 storage error for {filename}: {e}")
                storage_results["aws_error"] = str(e)
        
        # Google Cloud Storage
        if self.gcp_enabled:
            try:
                gcp_clients = await self._get_gcp_clients()
                bucket = gcp_clients['storage'].bucket(settings.gcp.storage_bucket)
                blob = bucket.blob(full_path)
                blob.upload_from_string(file_content, content_type=self._get_content_type(filename))
                storage_results["gcp"] = f"gs://{settings.gcp.storage_bucket}/{full_path}"
                logger.info(f"Document stored in GCP: {filename}")
            except Exception as e:
                logger.error(f"GCP storage error for {filename}: {e}")
                storage_results["gcp_error"] = str(e)
        
        return storage_results
    
    async def process_document_azure(self, blob_url: str, document_id: str) -> Dict[str, Any]:
        """Process document using Azure Computer Vision."""
        if not self.azure_enabled:
            return {"provider": "azure", "error": "Azure not enabled", "status": "error"}
        
        try:
            azure_clients = await self._get_azure_clients()
            cv_client = azure_clients['cv']
            
            if not cv_client:
                return {"provider": "azure", "error": "Computer Vision client not available", "status": "error"}
            
            # Start OCR operation
            read_response = cv_client.read(blob_url, raw=True)
            operation_id = read_response.headers["Operation-Location"].split("/")[-1]
            
            # Wait for operation to complete
            max_attempts = 30
            attempt = 0
            while attempt < max_attempts:
                read_result = cv_client.get_read_result(operation_id)
                if read_result.status.lower() not in ['notstarted', 'running']:
                    break
                await asyncio.sleep(2)
                attempt += 1
            
            if read_result.status.lower() == 'succeeded':
                extracted_text = ""
                for text_result in read_result.analyze_result.read_results:
                    for line in text_result.lines:
                        extracted_text += line.text + "\n"
                
                result = {
                    "provider": "azure",
                    "text": extracted_text,
                    "confidence": "95%",  # Azure typically has high confidence
                    "status": "success",
                    "processing_time": attempt * 2
                }
                
                log_document_processing(document_id, "unknown", "azure", True, {"confidence": "95%"})
                return result
            else:
                error_msg = f"OCR operation failed with status: {read_result.status}"
                log_document_processing(document_id, "unknown", "azure", False, {"error": error_msg})
                return {"provider": "azure", "error": error_msg, "status": "error"}
                
        except Exception as e:
            error_msg = str(e)
            log_document_processing(document_id, "unknown", "azure", False, {"error": error_msg})
            return {"provider": "azure", "error": error_msg, "status": "error"}
    
    async def process_document_aws(self, s3_bucket: str, s3_key: str, document_id: str) -> Dict[str, Any]:
        """Process document using AWS Textract."""
        if not self.aws_enabled:
            return {"provider": "aws", "error": "AWS not enabled", "status": "error"}
        
        try:
            aws_clients = await self._get_aws_clients()
            textract = aws_clients['textract']
            
            response = textract.detect_document_text(
                Document={
                    'S3Object': {
                        'Bucket': s3_bucket,
                        'Name': s3_key
                    }
                }
            )
            
            # Extract text
            extracted_text = ""
            confidence_scores = []
            
            for item in response['Blocks']:
                if item['BlockType'] == 'LINE':
                    extracted_text += item['Text'] + "\n"
                    if 'Confidence' in item:
                        confidence_scores.append(item['Confidence'])
            
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 90
            
            result = {
                "provider": "aws",
                "text": extracted_text,
                "confidence": f"{avg_confidence:.1f}%",
                "status": "success",
                "blocks_processed": len(response['Blocks'])
            }
            
            log_document_processing(document_id, "unknown", "aws", True, {"confidence": f"{avg_confidence:.1f}%"})
            return result
            
        except Exception as e:
            error_msg = str(e)
            log_document_processing(document_id, "unknown", "aws", False, {"error": error_msg})
            return {"provider": "aws", "error": error_msg, "status": "error"}
    
    async def process_document_gcp(self, gcs_uri: str, document_id: str) -> Dict[str, Any]:
        """Process document using Google Cloud Document AI."""
        if not self.gcp_enabled:
            return {"provider": "gcp", "error": "GCP not enabled", "status": "error"}
        
        try:
            gcp_clients = await self._get_gcp_clients()
            document_ai = gcp_clients['document_ai']
            
            # Configure the request
            request = {
                "name": settings.gcp.processor_name,
                "document": {
                    "gcs_document": {"gcs_uri": gcs_uri}
                }
            }
            
            # Process document
            result = document_ai.process_document(request=request)
            document = result.document
            
            # Extract entities
            entities = []
            for entity in document.entities:
                entities.append({
                    "type": entity.type_,
                    "text": entity.text_anchor.content if entity.text_anchor else "",
                    "confidence": entity.confidence
                })
            
            processing_result = {
                "provider": "gcp",
                "text": document.text,
                "confidence": "92%",  # GCP typically has good confidence
                "entities": entities,
                "status": "success"
            }
            
            log_document_processing(document_id, "unknown", "gcp", True, {"entities_found": len(entities)})
            return processing_result
            
        except Exception as e:
            error_msg = str(e)
            log_document_processing(document_id, "unknown", "gcp", False, {"error": error_msg})
            return {"provider": "gcp", "error": error_msg, "status": "error"}
    
    async def send_email_azure(self, to_email: str, subject: str, body: str) -> bool:
        """Send email using Azure Communication Services."""
        if not self.azure_enabled:
            return False
        
        try:
            azure_clients = await self._get_azure_clients()
            email_client = azure_clients['email']
            
            if not email_client:
                return False
            
            message = {
                "senderAddress": settings.azure.email_sender,
                "recipients": {
                    "to": [{"address": to_email}]
                },
                "content": {
                    "subject": subject,
                    "html": body
                }
            }
            
            poller = email_client.begin_send(message)
            result = poller.result()
            
            log_email_event("azure", to_email, subject, True)
            return True
            
        except Exception as e:
            logger.error(f"Azure email error: {e}")
            log_email_event("azure", to_email, subject, False)
            return False
    
    async def send_email_aws(self, to_email: str, subject: str, body: str) -> bool:
        """Send email using AWS SES."""
        if not self.aws_enabled:
            return False
        
        try:
            aws_clients = await self._get_aws_clients()
            ses = aws_clients['ses']
            
            response = ses.send_email(
                Source=settings.aws.email_sender,
                Destination={'ToAddresses': [to_email]},
                Message={
                    'Subject': {'Data': subject},
                    'Body': {'Html': {'Data': body}}
                }
            )
            
            log_email_event("aws", to_email, subject, True)
            return True
            
        except Exception as e:
            logger.error(f"AWS email error: {e}")
            log_email_event("aws", to_email, subject, False)
            return False
    
    def _get_content_type(self, filename: str) -> str:
        """Get content type based on file extension."""
        extension = filename.lower().split('.')[-1]
        content_types = {
            'pdf': 'application/pdf',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'png': 'image/png',
            'gif': 'image/gif',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'txt': 'text/plain'
        }
        return content_types.get(extension, 'application/octet-stream')
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all cloud services."""
        health = {
            "azure": {"enabled": self.azure_enabled, "services": {}},
            "aws": {"enabled": self.aws_enabled, "services": {}},
            "gcp": {"enabled": self.gcp_enabled, "services": {}}
        }
        
        # Test Azure services
        if self.azure_enabled:
            try:
                azure_clients = await self._get_azure_clients()
                # Test blob storage
                if 'blob' in azure_clients:
                    azure_clients['blob'].get_account_information()
                    health["azure"]["services"]["storage"] = "healthy"
            except Exception as e:
                health["azure"]["services"]["storage"] = f"error: {str(e)}"
        
        # Test AWS services
        if self.aws_enabled:
            try:
                aws_clients = await self._get_aws_clients()
                # Test S3
                if 's3' in aws_clients:
                    aws_clients['s3'].list_buckets()
                    health["aws"]["services"]["s3"] = "healthy"
            except Exception as e:
                health["aws"]["services"]["s3"] = f"error: {str(e)}"
        
        # Test GCP services
        if self.gcp_enabled:
            try:
                gcp_clients = await self._get_gcp_clients()
                # Test storage
                if 'storage' in gcp_clients:
                    list(gcp_clients['storage'].list_buckets(max_results=1))
                    health["gcp"]["services"]["storage"] = "healthy"
            except Exception as e:
                health["gcp"]["services"]["storage"] = f"error: {str(e)}"
        
        return health
