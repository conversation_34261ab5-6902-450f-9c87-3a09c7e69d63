"""
HumanLayer integration service for ARIA system.
Implements human-as-tool pattern for expert interactions across multiple channels.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Callable
from uuid import uuid4

from humanlayer import HumanLayer, ContactChannel, SlackContactChannel, EmailContactChannel, WhatsAppContactChannel

from ..config import settings
from ..models.human_interactions import ExpertType, InteractionStatus, InteractionChannel, UrgencyLevel
from ..utils.logger import get_logger, log_human_interaction

logger = get_logger(__name__)


class HumanToolsManager:
    """Manages human expert tools using HumanLayer human-as-tool pattern."""
    
    def __init__(self):
        self.hl = HumanLayer(
            api_key=settings.ai.humanlayer_api_key,
            run_id=settings.ai.humanlayer_run_id
        )
        self.expert_configs = self._setup_expert_configurations()
        self.active_interactions = {}
        
        logger.info("HumanLayer tools manager initialized")
    
    def _setup_expert_configurations(self) -> Dict[ExpertType, Dict[str, Any]]:
        """Setup expert configurations with contact channels and capabilities."""
        return {
            ExpertType.CLAIMS_REVIEWER: {
                "contact_channel": ContactChannel(
                    slack=SlackContactChannel(
                        channel_or_user_id="C1234567890",
                        context_about_channel_or_user="Primary claims review team for standard liability cases"
                    )
                ),
                "description": "Expert claims reviewer for standard liability cases",
                "expertise_areas": ["auto_liability", "property_damage", "coverage_analysis"],
                "escalation_threshold": 0.7,
                "expected_response_time": 240,  # 4 hours
                "availability": "24/7"
            },
            
            ExpertType.SENIOR_ADJUSTER: {
                "contact_channel": ContactChannel(
                    email=EmailContactChannel(
                        email_address="<EMAIL>",
                        context_about_user="Senior adjuster with 15+ years experience in complex claims"
                    )
                ),
                "description": "Senior adjuster for complex and high-value claims",
                "expertise_areas": ["complex_liability", "bodily_injury", "legal_analysis", "high_value_claims"],
                "escalation_threshold": 0.5,
                "expected_response_time": 120,  # 2 hours
                "availability": "Business hours"
            },
            
            ExpertType.FRAUD_INVESTIGATOR: {
                "contact_channel": ContactChannel(
                    email=EmailContactChannel(
                        email_address="<EMAIL>",
                        context_about_user="Specialized fraud investigation team"
                    )
                ),
                "description": "Fraud investigation specialist for suspicious claims",
                "expertise_areas": ["fraud_detection", "investigation", "evidence_analysis", "pattern_recognition"],
                "escalation_threshold": 0.3,
                "expected_response_time": 480,  # 8 hours
                "availability": "Business hours"
            },
            
            ExpertType.MANAGER_APPROVER: {
                "contact_channel": ContactChannel(
                    whatsapp=WhatsAppContactChannel(
                        phone_number="+1234567890",
                        context_about_user="Claims manager for urgent high-value approvals"
                    )
                ),
                "description": "Manager approval for high-value claims over $50K",
                "expertise_areas": ["high_value_claims", "policy_interpretation", "settlement_authority"],
                "escalation_threshold": 0.8,
                "expected_response_time": 60,  # 1 hour
                "availability": "24/7 for urgent matters"
            },
            
            ExpertType.CUSTOMER_SERVICE: {
                "contact_channel": ContactChannel(
                    slack=SlackContactChannel(
                        channel_or_user_id="C0987654321",
                        context_about_channel_or_user="Customer service team for claimant communication"
                    )
                ),
                "description": "Customer service specialist for claimant communication",
                "expertise_areas": ["customer_communication", "claim_status", "documentation_requests"],
                "escalation_threshold": 0.9,
                "expected_response_time": 30,  # 30 minutes
                "availability": "Business hours"
            },
            
            ExpertType.LEGAL_COUNSEL: {
                "contact_channel": ContactChannel(
                    email=EmailContactChannel(
                        email_address="<EMAIL>",
                        context_about_user="Legal counsel for coverage disputes and litigation"
                    )
                ),
                "description": "Legal counsel for coverage disputes and potential litigation",
                "expertise_areas": ["legal_analysis", "coverage_disputes", "litigation_risk", "regulatory_compliance"],
                "escalation_threshold": 0.4,
                "expected_response_time": 720,  # 12 hours
                "availability": "Business hours"
            }
        }
    
    def create_human_tool(self, expert_type: ExpertType) -> Callable:
        """Create a human-as-tool function for the specified expert type."""
        config = self.expert_configs[expert_type]
        
        async def human_tool_function(
            request: str,
            context: Optional[Dict[str, Any]] = None,
            urgency: str = "normal",
            claim_id: Optional[str] = None
        ) -> Dict[str, Any]:
            """
            Contact human expert for assistance using HumanLayer human-as-tool pattern.
            
            Args:
                request: The specific request or question for the human expert
                context: Additional context information (claim details, analysis, etc.)
                urgency: Priority level (low, normal, high, urgent)
                claim_id: Associated claim ID for tracking
            
            Returns:
                Dictionary containing expert response and interaction details
            """
            return await self._execute_human_tool(expert_type, request, context, urgency, claim_id)
        
        # Set function metadata for LLM tool calling
        human_tool_function.__name__ = f"contact_{expert_type.value}"
        human_tool_function.__doc__ = f"""
        Contact {config['description']}
        
        Expertise areas: {', '.join(config['expertise_areas'])}
        Expected response time: {config['expected_response_time']} minutes
        Availability: {config['availability']}
        
        Use this tool when you need human expertise in: {', '.join(config['expertise_areas'])}
        """
        
        return human_tool_function
    
    async def _execute_human_tool(
        self,
        expert_type: ExpertType,
        request: str,
        context: Optional[Dict[str, Any]],
        urgency: str,
        claim_id: Optional[str]
    ) -> Dict[str, Any]:
        """Execute the human tool interaction using HumanLayer."""
        
        interaction_id = f"INT-{datetime.now().strftime('%Y%m%d%H%M%S')}-{str(uuid4())[:8]}"
        config = self.expert_configs[expert_type]
        
        # Format the request for human consumption
        formatted_request = self._format_human_request(
            request, context, urgency, config, claim_id, interaction_id
        )
        
        try:
            # Create human-as-tool interaction
            human_tool = self.hl.human_as_tool(
                contact_channel=config["contact_channel"]
            )
            
            # Track interaction start
            interaction_data = {
                "interaction_id": interaction_id,
                "expert_type": expert_type.value,
                "claim_id": claim_id,
                "request": request,
                "context": context,
                "urgency": urgency,
                "status": InteractionStatus.PENDING.value,
                "started_at": datetime.utcnow().isoformat(),
                "expected_response_time": config["expected_response_time"]
            }
            
            self.active_interactions[interaction_id] = interaction_data
            
            log_human_interaction(
                interaction_id,
                expert_type.value,
                "Human tool interaction started",
                {"claim_id": claim_id, "urgency": urgency}
            )
            
            # Execute the human interaction
            # Note: In a real implementation, this would be async and handled via webhooks
            # For demo purposes, we'll simulate the interaction
            if settings.is_development():
                response = await self._simulate_human_response(expert_type, request, context)
            else:
                response = await human_tool(formatted_request)
            
            # Update interaction status
            interaction_data["status"] = InteractionStatus.COMPLETED.value
            interaction_data["completed_at"] = datetime.utcnow().isoformat()
            interaction_data["response"] = response
            
            log_human_interaction(
                interaction_id,
                expert_type.value,
                "Human tool interaction completed",
                {"response_length": len(str(response))}
            )
            
            return {
                "interaction_id": interaction_id,
                "expert_type": expert_type.value,
                "status": "completed",
                "response": response,
                "response_time_minutes": self._calculate_response_time(interaction_data),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error in human tool interaction {interaction_id}: {e}")
            
            # Update interaction with error
            if interaction_id in self.active_interactions:
                self.active_interactions[interaction_id]["status"] = InteractionStatus.FAILED.value
                self.active_interactions[interaction_id]["error"] = str(e)
            
            log_human_interaction(
                interaction_id,
                expert_type.value,
                "Human tool interaction failed",
                {"error": str(e)}
            )
            
            return {
                "interaction_id": interaction_id,
                "expert_type": expert_type.value,
                "status": "failed",
                "error": str(e),
                "success": False
            }
    
    def _format_human_request(
        self,
        request: str,
        context: Optional[Dict[str, Any]],
        urgency: str,
        config: Dict[str, Any],
        claim_id: Optional[str],
        interaction_id: str
    ) -> str:
        """Format the request for human consumption."""
        
        urgency_emoji = {
            "low": "🟢",
            "normal": "🟡",
            "high": "🟠",
            "urgent": "🔴",
            "critical": "🚨"
        }
        
        formatted = f"""
{urgency_emoji.get(urgency, '🟡')} **{urgency.upper()} PRIORITY REQUEST**

**Interaction ID:** {interaction_id}
**Expert Type:** {config['description']}
**Claim ID:** {claim_id or 'N/A'}

**Request:**
{request}
"""
        
        if context:
            formatted += "\n**Context Information:**\n"
            for key, value in context.items():
                if key != 'claim_id':  # Avoid duplication
                    formatted += f"• **{key.replace('_', ' ').title()}:** {value}\n"
        
        formatted += f"""
**Your Expertise:** {', '.join(config['expertise_areas'])}
**Expected Response Time:** {config['expected_response_time']} minutes

Please provide your expert analysis and recommendations.

---
*This request was generated by ARIA (Autonomous Risk Intelligence Agent)*
"""
        
        return formatted
    
    async def _simulate_human_response(
        self,
        expert_type: ExpertType,
        request: str,
        context: Optional[Dict[str, Any]]
    ) -> str:
        """Simulate human expert response for development/demo purposes."""
        
        # Simulate processing time
        await asyncio.sleep(2)
        
        responses = {
            ExpertType.CLAIMS_REVIEWER: f"""
Based on my review of the claim details, I recommend the following:

1. **Coverage Assessment:** The claim appears to fall within policy coverage based on the incident description.

2. **Documentation:** The submitted documents are sufficient for processing. All required forms are present.

3. **Liability Analysis:** Initial assessment suggests standard liability determination can proceed.

4. **Recommendation:** Approve for standard processing with estimated settlement range of $15,000-$25,000.

5. **Next Steps:** Proceed with settlement negotiation and prepare final documentation.

**Confidence Level:** High
**Processing Priority:** Standard

Please let me know if you need any additional analysis or have questions about this assessment.
            """,
            
            ExpertType.SENIOR_ADJUSTER: f"""
After thorough review of this complex claim, here is my expert assessment:

**Claim Complexity Analysis:**
- Multiple parties involved requiring detailed liability assessment
- Policy interpretation needed for coverage determination
- Potential subrogation opportunities identified

**Coverage Determination:**
- Primary coverage applies under Section 3.2.1 of the policy
- Deductible: $1,000 applies
- Coverage limits: $100,000 per occurrence

**Settlement Recommendation:**
- Estimated settlement: $45,000-$65,000
- Recommend independent adjuster for property assessment
- Legal review suggested due to third-party involvement

**Risk Factors:**
- Monitor for potential fraud indicators
- Ensure all medical documentation is current
- Verify policy was active at time of incident

**Approval:** Recommend approval with conditions outlined above.
            """,
            
            ExpertType.FRAUD_INVESTIGATOR: f"""
**Fraud Investigation Assessment:**

**Initial Screening Results:**
- No immediate red flags identified in claim submission
- Timeline of events appears consistent
- Documentation provided seems authentic

**Risk Indicators Checked:**
✅ Claimant history - No previous suspicious claims
✅ Incident timing - Consistent with normal patterns
✅ Documentation quality - Appears legitimate
✅ Witness statements - Corroborate claimant's account

**Recommendation:**
- **Risk Level:** LOW
- **Action:** Proceed with standard processing
- **Monitoring:** No additional surveillance required

**Additional Notes:**
- All submitted documents pass initial authenticity checks
- No patterns matching known fraud schemes
- Claimant cooperation level is appropriate

**Clearance:** Approved for standard claims processing.
            """,
            
            ExpertType.MANAGER_APPROVER: f"""
**MANAGEMENT APPROVAL DECISION**

**Claim Review Summary:**
- High-value claim requiring management oversight
- AI analysis confidence level reviewed
- Expert recommendations considered

**Financial Impact:**
- Estimated settlement: ${context.get('estimated_value', 75000):,} if context else $75,000
- Within acceptable risk parameters
- Budget impact: Approved

**Decision Factors:**
- Policy coverage confirmed
- Liability assessment reasonable
- Settlement amount justified by damages

**APPROVAL GRANTED**

**Conditions:**
- Final settlement documentation required
- Legal review completed satisfactorily
- All regulatory requirements met

**Authorization Code:** MGR-{datetime.now().strftime('%Y%m%d')}-{str(uuid4())[:6].upper()}

Approved by: Claims Manager
Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}
            """,
            
            ExpertType.CUSTOMER_SERVICE: f"""
**Customer Service Response:**

Thank you for bringing this to our attention. I've reviewed the claim status and can provide the following update:

**Current Status:** Your claim is progressing well through our review process.

**Next Steps for Customer:**
1. We will contact you within 24 hours with a status update
2. Please ensure your contact information is current
3. Any additional documentation will be requested if needed

**Customer Communication Plan:**
- Status update email will be sent today
- Phone call scheduled for tomorrow morning
- Final decision communication within 48 hours

**Customer Satisfaction:**
- Is there anything specific you'd like us to address?
- Do you have any immediate concerns or questions?
- We're committed to resolving this efficiently

**Internal Notes:**
- Customer has been very cooperative
- No communication issues identified
- Standard follow-up protocol applies

Please let me know if you need any additional customer service support.
            """,
            
            ExpertType.LEGAL_COUNSEL: f"""
**Legal Counsel Assessment:**

**Legal Review Summary:**
I have reviewed the claim for potential legal implications and coverage issues.

**Coverage Analysis:**
- Policy language clearly supports coverage for this type of incident
- No exclusions apply to the circumstances described
- Liability determination falls within standard parameters

**Legal Risk Assessment:**
- **Litigation Risk:** LOW
- **Regulatory Compliance:** All requirements met
- **Third-Party Exposure:** Minimal

**Recommendations:**
1. Proceed with settlement as recommended by adjusters
2. Standard release documentation is sufficient
3. No additional legal protections required

**Regulatory Considerations:**
- All provincial insurance regulations complied with
- Reporting requirements satisfied
- Documentation meets legal standards

**Legal Clearance:** APPROVED

No legal impediments to proceeding with claim settlement.

**Attorney:** Legal Counsel
**Date:** {datetime.now().strftime('%Y-%m-%d')}
**Case Reference:** LC-{str(uuid4())[:8].upper()}
            """
        }
        
        return responses.get(expert_type, f"Expert response for {expert_type.value}: {request}")
    
    def _calculate_response_time(self, interaction_data: Dict[str, Any]) -> Optional[int]:
        """Calculate response time in minutes."""
        try:
            start_time = datetime.fromisoformat(interaction_data["started_at"])
            end_time = datetime.fromisoformat(interaction_data.get("completed_at", datetime.utcnow().isoformat()))
            return int((end_time - start_time).total_seconds() / 60)
        except (ValueError, KeyError):
            return None
    
    def get_all_human_tools(self) -> List[Callable]:
        """Get all human tools for LLM agent."""
        return [self.create_human_tool(expert_type) for expert_type in ExpertType]
    
    def get_human_tool(self, expert_type: ExpertType) -> Callable:
        """Get specific human tool."""
        return self.create_human_tool(expert_type)
    
    def get_active_interactions(self) -> Dict[str, Dict[str, Any]]:
        """Get all active interactions."""
        return self.active_interactions.copy()
    
    def get_interaction_status(self, interaction_id: str) -> Optional[Dict[str, Any]]:
        """Get status of specific interaction."""
        return self.active_interactions.get(interaction_id)
    
    async def check_overdue_interactions(self) -> List[Dict[str, Any]]:
        """Check for overdue interactions and handle escalation."""
        overdue_interactions = []
        current_time = datetime.utcnow()
        
        for interaction_id, interaction_data in self.active_interactions.items():
            if interaction_data["status"] in [InteractionStatus.PENDING.value, InteractionStatus.SENT.value]:
                start_time = datetime.fromisoformat(interaction_data["started_at"])
                expected_completion = start_time + timedelta(minutes=interaction_data["expected_response_time"])
                
                if current_time > expected_completion:
                    interaction_data["overdue"] = True
                    interaction_data["overdue_minutes"] = int((current_time - expected_completion).total_seconds() / 60)
                    overdue_interactions.append(interaction_data)
                    
                    log_human_interaction(
                        interaction_id,
                        interaction_data["expert_type"],
                        "Interaction overdue",
                        {"overdue_minutes": interaction_data["overdue_minutes"]}
                    )
        
        return overdue_interactions
    
    async def escalate_interaction(self, interaction_id: str, escalation_reason: str) -> bool:
        """Escalate an interaction to a higher level expert."""
        if interaction_id not in self.active_interactions:
            return False
        
        interaction_data = self.active_interactions[interaction_id]
        current_expert = ExpertType(interaction_data["expert_type"])
        
        # Define escalation paths
        escalation_paths = {
            ExpertType.CLAIMS_REVIEWER: ExpertType.SENIOR_ADJUSTER,
            ExpertType.SENIOR_ADJUSTER: ExpertType.MANAGER_APPROVER,
            ExpertType.CUSTOMER_SERVICE: ExpertType.SENIOR_ADJUSTER,
            ExpertType.FRAUD_INVESTIGATOR: ExpertType.LEGAL_COUNSEL
        }
        
        escalated_to = escalation_paths.get(current_expert)
        if not escalated_to:
            return False
        
        # Create new escalated interaction
        escalated_request = f"""
**ESCALATED REQUEST**
Original Expert: {current_expert.value}
Escalation Reason: {escalation_reason}

Original Request:
{interaction_data['request']}

Please provide urgent review and guidance.
        """
        
        escalated_interaction = await self._execute_human_tool(
            escalated_to,
            escalated_request,
            interaction_data.get('context'),
            "urgent",
            interaction_data.get('claim_id')
        )
        
        # Update original interaction
        interaction_data["escalated"] = True
        interaction_data["escalated_to"] = escalated_to.value
        interaction_data["escalation_reason"] = escalation_reason
        interaction_data["escalated_interaction_id"] = escalated_interaction["interaction_id"]
        
        log_human_interaction(
            interaction_id,
            current_expert.value,
            f"Escalated to {escalated_to.value}",
            {"reason": escalation_reason}
        )
        
        return True


class HumanInteractionService:
    """Main service for managing human interactions."""
    
    def __init__(self):
        self.tools_manager = HumanToolsManager()
        
    async def contact_expert(
        self,
        expert_type: ExpertType,
        request: str,
        context: Optional[Dict[str, Any]] = None,
        urgency: str = "normal",
        claim_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Contact a human expert."""
        human_tool = self.tools_manager.get_human_tool(expert_type)
        return await human_tool(request, context, urgency, claim_id)
    
    def get_all_expert_tools(self) -> List[Callable]:
        """Get all expert tools for AI agent."""
        return self.tools_manager.get_all_human_tools()
    
    async def get_interaction_status(self, interaction_id: str) -> Optional[Dict[str, Any]]:
        """Get interaction status."""
        return self.tools_manager.get_interaction_status(interaction_id)
    
    async def check_overdue_interactions(self) -> List[Dict[str, Any]]:
        """Check for overdue interactions."""
        return await self.tools_manager.check_overdue_interactions()
    
    async def escalate_interaction(self, interaction_id: str, reason: str) -> bool:
        """Escalate an interaction."""
        return await self.tools_manager.escalate_interaction(interaction_id, reason)
