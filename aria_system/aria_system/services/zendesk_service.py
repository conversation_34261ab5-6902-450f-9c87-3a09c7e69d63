"""
ARIA Zendesk Integration Service
Complete Zendesk API integration for ticket management and synchronization
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

import aiohttp
import aiofiles
from base64 import b64encode

from ..config import Settings
from ..database.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)


class ZendeskService:
    """Zendesk API integration for claim ticket management"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        
        # Zendesk configuration
        self.subdomain = settings.zendesk.subdomain
        self.email = settings.zendesk.email
        self.api_token = settings.zendesk.api_token
        self.base_url = f"https://{self.subdomain}.zendesk.com/api/v2"
        
        # Authentication
        credentials = f"{self.email}/token:{self.api_token}"
        self.auth_header = b64encode(credentials.encode()).decode()
        
        # Default ticket settings
        self.default_group_id = settings.zendesk.group_id
        self.default_priority = settings.zendesk.priority
        self.default_type = settings.zendesk.type
        self.default_tags = settings.zendesk.tags.split(',') if settings.zendesk.tags else []
    
    async def create_ticket(
        self,
        subject: str,
        description: str,
        requester_email: str,
        requester_name: str = None,
        attachments: List[Dict] = None,
        priority: str = None,
        tags: List[str] = None,
        custom_fields: Dict = None
    ) -> int:
        """Create a new Zendesk ticket"""
        try:
            # Prepare ticket data
            ticket_data = {
                "ticket": {
                    "subject": subject,
                    "comment": {
                        "body": description
                    },
                    "requester": {
                        "email": requester_email,
                        "name": requester_name or requester_email
                    },
                    "priority": priority or self.default_priority,
                    "type": self.default_type,
                    "group_id": self.default_group_id,
                    "tags": (tags or []) + self.default_tags
                }
            }
            
            # Add custom fields if provided
            if custom_fields:
                ticket_data["ticket"]["custom_fields"] = [
                    {"id": field_id, "value": value}
                    for field_id, value in custom_fields.items()
                ]
            
            # Upload attachments first if any
            if attachments:
                attachment_tokens = await self._upload_attachments(attachments)
                if attachment_tokens:
                    ticket_data["ticket"]["comment"]["uploads"] = attachment_tokens
            
            # Create ticket
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                async with session.post(
                    f"{self.base_url}/tickets.json",
                    json=ticket_data,
                    headers=headers
                ) as response:
                    if response.status == 201:
                        result = await response.json()
                        ticket_id = result["ticket"]["id"]
                        
                        logger.info(f"Created Zendesk ticket {ticket_id}")
                        
                        # Store ticket info in database
                        await self._store_ticket_info(ticket_id, result["ticket"])
                        
                        return ticket_id
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to create Zendesk ticket: {response.status} - {error_text}")
                        raise Exception(f"Zendesk API error: {response.status}")
        
        except Exception as e:
            logger.error(f"Error creating Zendesk ticket: {e}")
            raise
    
    async def add_comment(
        self,
        ticket_id: int,
        comment: str,
        author_email: str = None,
        is_public: bool = True,
        attachments: List[Dict] = None
    ) -> bool:
        """Add a comment to an existing ticket"""
        try:
            comment_data = {
                "ticket": {
                    "comment": {
                        "body": comment,
                        "public": is_public
                    }
                }
            }
            
            # Set author if provided
            if author_email:
                comment_data["ticket"]["comment"]["author_id"] = await self._get_user_id(author_email)
            
            # Upload attachments if any
            if attachments:
                attachment_tokens = await self._upload_attachments(attachments)
                if attachment_tokens:
                    comment_data["ticket"]["comment"]["uploads"] = attachment_tokens
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                async with session.put(
                    f"{self.base_url}/tickets/{ticket_id}.json",
                    json=comment_data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        logger.info(f"Added comment to Zendesk ticket {ticket_id}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to add comment: {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            logger.error(f"Error adding comment to ticket {ticket_id}: {e}")
            return False
    
    async def update_ticket_status(
        self,
        ticket_id: int,
        status: str,
        comment: str = None
    ) -> bool:
        """Update ticket status"""
        try:
            update_data = {
                "ticket": {
                    "status": status
                }
            }
            
            # Add comment if provided
            if comment:
                update_data["ticket"]["comment"] = {
                    "body": comment,
                    "public": False
                }
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                async with session.put(
                    f"{self.base_url}/tickets/{ticket_id}.json",
                    json=update_data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        logger.info(f"Updated Zendesk ticket {ticket_id} status to {status}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to update ticket status: {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            logger.error(f"Error updating ticket {ticket_id} status: {e}")
            return False
    
    async def assign_ticket(
        self,
        ticket_id: int,
        assignee_email: str,
        comment: str = None
    ) -> bool:
        """Assign ticket to an agent"""
        try:
            assignee_id = await self._get_user_id(assignee_email)
            if not assignee_id:
                logger.error(f"Could not find Zendesk user for email {assignee_email}")
                return False
            
            update_data = {
                "ticket": {
                    "assignee_id": assignee_id
                }
            }
            
            # Add comment if provided
            if comment:
                update_data["ticket"]["comment"] = {
                    "body": comment,
                    "public": False
                }
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                async with session.put(
                    f"{self.base_url}/tickets/{ticket_id}.json",
                    json=update_data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        logger.info(f"Assigned Zendesk ticket {ticket_id} to {assignee_email}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to assign ticket: {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            logger.error(f"Error assigning ticket {ticket_id}: {e}")
            return False
    
    async def add_cc_to_ticket(
        self,
        ticket_id: int,
        cc_emails: List[str]
    ) -> bool:
        """Add CC recipients to a ticket"""
        try:
            # Get current ticket to preserve existing CCs
            ticket_info = await self.get_ticket(ticket_id)
            if not ticket_info:
                return False
            
            current_ccs = ticket_info.get("collaborator_ids", [])
            
            # Get user IDs for new CC emails
            new_cc_ids = []
            for email in cc_emails:
                user_id = await self._get_user_id(email)
                if user_id and user_id not in current_ccs:
                    new_cc_ids.append(user_id)
            
            if not new_cc_ids:
                return True  # No new CCs to add
            
            update_data = {
                "ticket": {
                    "collaborator_ids": current_ccs + new_cc_ids
                }
            }
            
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                async with session.put(
                    f"{self.base_url}/tickets/{ticket_id}.json",
                    json=update_data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        logger.info(f"Added CCs to Zendesk ticket {ticket_id}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to add CCs: {response.status} - {error_text}")
                        return False
        
        except Exception as e:
            logger.error(f"Error adding CCs to ticket {ticket_id}: {e}")
            return False
    
    async def get_ticket(self, ticket_id: int) -> Optional[Dict]:
        """Get ticket information"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                async with session.get(
                    f"{self.base_url}/tickets/{ticket_id}.json",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result["ticket"]
                    else:
                        logger.error(f"Failed to get ticket {ticket_id}: {response.status}")
                        return None
        
        except Exception as e:
            logger.error(f"Error getting ticket {ticket_id}: {e}")
            return None
    
    async def _upload_attachments(self, attachments: List[Dict]) -> List[str]:
        """Upload attachments to Zendesk"""
        tokens = []
        
        for attachment in attachments:
            try:
                file_path = Path(attachment['file_path'])
                if not file_path.exists():
                    logger.warning(f"Attachment file not found: {file_path}")
                    continue
                
                async with aiofiles.open(file_path, 'rb') as f:
                    file_data = await f.read()
                
                async with aiohttp.ClientSession() as session:
                    headers = {
                        "Authorization": f"Basic {self.auth_header}",
                        "Content-Type": attachment.get('content_type', 'application/octet-stream')
                    }
                    
                    params = {
                        "filename": attachment['filename']
                    }
                    
                    async with session.post(
                        f"{self.base_url}/uploads.json",
                        data=file_data,
                        headers=headers,
                        params=params
                    ) as response:
                        if response.status == 201:
                            result = await response.json()
                            tokens.append(result["upload"]["token"])
                            logger.info(f"Uploaded attachment: {attachment['filename']}")
                        else:
                            logger.error(f"Failed to upload {attachment['filename']}: {response.status}")
            
            except Exception as e:
                logger.error(f"Error uploading attachment {attachment['filename']}: {e}")
        
        return tokens
    
    async def _get_user_id(self, email: str) -> Optional[int]:
        """Get Zendesk user ID by email"""
        try:
            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }
                
                params = {"query": f"email:{email}"}
                
                async with session.get(
                    f"{self.base_url}/users/search.json",
                    headers=headers,
                    params=params
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        users = result.get("users", [])
                        if users:
                            return users[0]["id"]
                    
                    return None
        
        except Exception as e:
            logger.error(f"Error getting user ID for {email}: {e}")
            return None
    
    async def _store_ticket_info(self, ticket_id: int, ticket_data: Dict):
        """Store ticket information in database"""
        try:
            ticket_info = {
                'ticket_id': ticket_id,
                'ticket_url': f"https://{self.subdomain}.zendesk.com/agent/tickets/{ticket_id}",
                'status': ticket_data.get('status'),
                'priority': ticket_data.get('priority'),
                'metadata': {
                    'created_at': ticket_data.get('created_at'),
                    'updated_at': ticket_data.get('updated_at'),
                    'requester_id': ticket_data.get('requester_id'),
                    'assignee_id': ticket_data.get('assignee_id')
                }
            }
            
            # This will be called after claim is created
            # For now, just log the ticket info
            logger.info(f"Ticket {ticket_id} info ready for storage")
            
        except Exception as e:
            logger.error(f"Error storing ticket info: {e}")
    
    async def update_ticket_status(
        self,
        ticket_id: int,
        status: str,
        comment: str = None
    ) -> bool:
        """Update ticket status"""
        try:
            update_data = {
                "ticket": {
                    "status": status
                }
            }

            # Add comment if provided
            if comment:
                update_data["ticket"]["comment"] = {
                    "body": comment,
                    "public": False
                }

            async with aiohttp.ClientSession() as session:
                headers = {
                    "Authorization": f"Basic {self.auth_header}",
                    "Content-Type": "application/json"
                }

                async with session.put(
                    f"{self.base_url}/tickets/{ticket_id}.json",
                    json=update_data,
                    headers=headers
                ) as response:
                    if response.status == 200:
                        logger.info(f"Updated Zendesk ticket {ticket_id} status to {status}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Failed to update ticket status: {response.status} - {error_text}")
                        return False

        except Exception as e:
            logger.error(f"Error updating ticket {ticket_id} status: {e}")
            return False

    async def sync_ticket_status(self, claim_id: str, ticket_id: int):
        """Sync ticket status with claim status"""
        try:
            ticket_info = await self.get_ticket(ticket_id)
            if ticket_info:
                # Update claim status based on ticket status
                await self.supabase.update_claim_from_ticket(claim_id, ticket_info)
                logger.info(f"Synced ticket {ticket_id} status with claim {claim_id}")

        except Exception as e:
            logger.error(f"Error syncing ticket status: {e}")
