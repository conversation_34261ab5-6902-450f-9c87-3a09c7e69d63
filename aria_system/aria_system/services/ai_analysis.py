"""
ARIA AI Document Analysis Service
Comprehensive claim analysis using Zurich OCR API and OpenAI
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

import aiohttp
import aiofiles
from openai import AsyncOpenAI

from ..config import Settings
from ..database.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)


class AIAnalysisService:
    """AI-powered document analysis and claim processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        
        # Initialize OpenAI client
        self.openai_client = AsyncOpenAI(
            api_key=settings.ai.openai_api_key
        )
        
        # Zurich OCR API configuration
        self.ocr_api_url = settings.ai.zurich_ocr_api_url
        self.ocr_api_key = settings.ai.zurich_ocr_api_key
        
        # Analysis prompts
        self.analysis_prompts = self._load_analysis_prompts()
    
    def _load_analysis_prompts(self) -> Dict[str, str]:
        """Load AI analysis prompts"""
        return {
            "claim_classification": """
            Analyze this insurance claim and provide a comprehensive assessment.
            
            Claim Information:
            - Subject: {subject}
            - Description: {description}
            - OCR Text: {ocr_text}
            
            Please provide analysis in JSON format:
            {{
                "claim_type": "auto|property|liability|medical|other",
                "incident_type": "collision|theft|fire|flood|injury|other",
                "severity": "low|medium|high|critical",
                "estimated_amount": number,
                "confidence_score": 0.0-1.0,
                "key_facts": ["fact1", "fact2", ...],
                "risk_factors": ["risk1", "risk2", ...],
                "required_documents": ["doc1", "doc2", ...],
                "recommendations": ["rec1", "rec2", ...],
                "fraud_indicators": ["indicator1", "indicator2", ...],
                "next_steps": ["step1", "step2", ...]
            }}
            """,
            
            "document_extraction": """
            Extract structured information from this insurance document OCR text.
            
            OCR Text: {ocr_text}
            Document Type: {document_type}
            
            Extract the following information in JSON format:
            {{
                "policy_number": "string",
                "claim_number": "string", 
                "incident_date": "YYYY-MM-DD",
                "incident_time": "HH:MM",
                "incident_location": "string",
                "parties_involved": [
                    {{
                        "name": "string",
                        "role": "claimant|witness|other_driver|police_officer",
                        "contact": "string"
                    }}
                ],
                "vehicle_details": [
                    {{
                        "make": "string",
                        "model": "string", 
                        "year": "number",
                        "license_plate": "string",
                        "vin": "string",
                        "damage_description": "string"
                    }}
                ],
                "monetary_amounts": [
                    {{
                        "type": "repair|medical|property|total",
                        "amount": number,
                        "currency": "USD"
                    }}
                ],
                "dates": [
                    {{
                        "type": "incident|report|medical_treatment",
                        "date": "YYYY-MM-DD"
                    }}
                ],
                "addresses": ["address1", "address2", ...],
                "phone_numbers": ["phone1", "phone2", ...],
                "key_statements": ["statement1", "statement2", ...]
            }}
            """,
            
            "fraud_detection": """
            Analyze this claim for potential fraud indicators.
            
            Claim Data: {claim_data}
            OCR Text: {ocr_text}
            
            Provide fraud analysis in JSON format:
            {{
                "fraud_risk_score": 0.0-1.0,
                "risk_level": "low|medium|high|critical",
                "fraud_indicators": [
                    {{
                        "indicator": "string",
                        "severity": "low|medium|high",
                        "description": "string"
                    }}
                ],
                "red_flags": ["flag1", "flag2", ...],
                "verification_needed": ["item1", "item2", ...],
                "recommended_actions": ["action1", "action2", ...],
                "investigation_priority": "low|medium|high|urgent"
            }}
            """
        }
    
    async def analyze_claim_documents(self, claim_id: str) -> Dict[str, Any]:
        """Perform comprehensive analysis of claim documents"""
        try:
            logger.info(f"Starting AI analysis for claim {claim_id}")
            
            # Get claim data and documents
            claim_data = await self.supabase.get_claim(claim_id)
            documents = await self.supabase.get_claim_documents(claim_id)
            
            if not claim_data:
                raise ValueError(f"Claim {claim_id} not found")
            
            # Process documents with OCR
            ocr_results = []
            for doc in documents:
                if not doc.get('is_processed'):
                    ocr_result = await self._process_document_ocr(doc)
                    if ocr_result:
                        ocr_results.append(ocr_result)
                        # Update document with OCR results
                        await self.supabase.update_document_ocr(doc['id'], ocr_result)
            
            # Combine all OCR text
            combined_ocr_text = "\n\n".join([
                result.get('text', '') for result in ocr_results
            ])
            
            # Perform AI analysis
            analysis_results = {}
            
            # 1. Claim classification and assessment
            classification = await self._classify_claim(
                claim_data, combined_ocr_text
            )
            analysis_results['classification'] = classification
            
            # 2. Document information extraction
            extraction = await self._extract_document_information(
                combined_ocr_text, documents
            )
            analysis_results['extraction'] = extraction
            
            # 3. Fraud detection analysis
            fraud_analysis = await self._analyze_fraud_risk(
                claim_data, combined_ocr_text
            )
            analysis_results['fraud_analysis'] = fraud_analysis
            
            # 4. Calculate overall confidence and recommendations
            overall_analysis = await self._generate_overall_analysis(
                claim_data, analysis_results
            )
            analysis_results['overall'] = overall_analysis
            
            # Store analysis results
            await self.supabase.store_ai_analysis(claim_id, analysis_results)
            
            # Update claim with analysis scores
            await self._update_claim_scores(claim_id, analysis_results)
            
            logger.info(f"Completed AI analysis for claim {claim_id}")
            return analysis_results
        
        except Exception as e:
            logger.error(f"Error in AI analysis for claim {claim_id}: {e}")
            raise
    
    async def _process_document_ocr(self, document: Dict) -> Optional[Dict]:
        """Process document with Zurich OCR API"""
        try:
            file_path = Path(document['file_path'])
            if not file_path.exists():
                logger.warning(f"Document file not found: {file_path}")
                return None
            
            # Prepare OCR request
            async with aiofiles.open(file_path, 'rb') as f:
                file_data = await f.read()
            
            # OCR configuration
            config = {
                "ocr_engine": "google",
                "google_processor": "OCR_PROCESSOR",
                "llm_routing_enabled": True,
                "post_processing": "v2",
                "preprocessing": "none",
                "parallel_processing": True
            }
            
            # Prepare multipart form data
            form_data = aiohttp.FormData()
            form_data.add_field(
                'files',
                file_data,
                filename=document['filename'],
                content_type=document.get('mime_type', 'application/octet-stream')
            )
            form_data.add_field('config', json.dumps(config))
            
            # Send OCR request
            headers = {}
            if self.ocr_api_key:
                headers['Authorization'] = f'Bearer {self.ocr_api_key}'
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.ocr_api_url,
                    data=form_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"OCR completed for {document['filename']}")
                        return self._process_ocr_response(result)
                    else:
                        error_text = await response.text()
                        logger.error(f"OCR failed for {document['filename']}: {response.status} - {error_text}")
                        return None
        
        except Exception as e:
            logger.error(f"Error processing OCR for {document['filename']}: {e}")
            return None
    
    def _process_ocr_response(self, ocr_response: Dict) -> Dict:
        """Process and normalize OCR API response"""
        try:
            # Extract text and confidence from OCR response
            # This will depend on the actual Zurich OCR API response format
            
            if 'results' in ocr_response:
                results = ocr_response['results']
                if results and len(results) > 0:
                    first_result = results[0]
                    
                    return {
                        'text': first_result.get('extracted_text', ''),
                        'confidence': first_result.get('confidence', 0.0),
                        'entities': first_result.get('entities', []),
                        'metadata': {
                            'processing_time': ocr_response.get('processing_time'),
                            'pages_processed': len(results),
                            'raw_response': ocr_response
                        }
                    }
            
            # Fallback for different response formats
            return {
                'text': str(ocr_response.get('text', '')),
                'confidence': float(ocr_response.get('confidence', 0.0)),
                'entities': ocr_response.get('entities', []),
                'metadata': {'raw_response': ocr_response}
            }
        
        except Exception as e:
            logger.error(f"Error processing OCR response: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'entities': [],
                'metadata': {'error': str(e)}
            }
    
    async def _classify_claim(self, claim_data: Dict, ocr_text: str) -> Dict:
        """Classify and assess the claim using AI"""
        try:
            prompt = self.analysis_prompts['claim_classification'].format(
                subject=claim_data.get('subject', ''),
                description=claim_data.get('description', ''),
                ocr_text=ocr_text[:4000]  # Limit text length
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.ai.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert insurance claim analyst. Provide accurate, detailed analysis in valid JSON format."},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.settings.ai.openai_temperature,
                max_tokens=self.settings.ai.openai_max_tokens
            )
            
            result_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                # Extract JSON from response if wrapped in other text
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise ValueError("No valid JSON found in response")
        
        except Exception as e:
            logger.error(f"Error in claim classification: {e}")
            return {
                "claim_type": "unknown",
                "confidence_score": 0.0,
                "error": str(e)
            }
    
    async def _extract_document_information(self, ocr_text: str, documents: List[Dict]) -> Dict:
        """Extract structured information from documents"""
        try:
            # Determine primary document type
            doc_type = "general"
            if documents:
                doc_type = documents[0].get('document_type', 'general')
            
            prompt = self.analysis_prompts['document_extraction'].format(
                ocr_text=ocr_text[:4000],
                document_type=doc_type
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.ai.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting structured information from insurance documents. Return valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Lower temperature for extraction
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content
            
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    return {"error": "Failed to parse extraction results"}
        
        except Exception as e:
            logger.error(f"Error in document extraction: {e}")
            return {"error": str(e)}
    
    async def _analyze_fraud_risk(self, claim_data: Dict, ocr_text: str) -> Dict:
        """Analyze claim for fraud risk"""
        try:
            prompt = self.analysis_prompts['fraud_detection'].format(
                claim_data=json.dumps(claim_data, default=str),
                ocr_text=ocr_text[:3000]
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.ai.openai_model,
                messages=[
                    {"role": "system", "content": "You are a fraud detection expert for insurance claims. Analyze carefully and provide detailed risk assessment in JSON format."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=1500
            )
            
            result_text = response.choices[0].message.content
            
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    return {"fraud_risk_score": 0.0, "risk_level": "unknown"}
        
        except Exception as e:
            logger.error(f"Error in fraud analysis: {e}")
            return {"fraud_risk_score": 0.0, "error": str(e)}
    
    async def _generate_overall_analysis(self, claim_data: Dict, analysis_results: Dict) -> Dict:
        """Generate overall analysis and recommendations"""
        try:
            classification = analysis_results.get('classification', {})
            extraction = analysis_results.get('extraction', {})
            fraud = analysis_results.get('fraud_analysis', {})
            
            # Calculate overall confidence
            confidence_scores = [
                classification.get('confidence_score', 0.0),
                fraud.get('fraud_risk_score', 0.0)  # Inverse for fraud
            ]
            overall_confidence = sum(confidence_scores) / len(confidence_scores)
            
            # Determine complexity
            complexity_factors = [
                len(extraction.get('parties_involved', [])),
                len(extraction.get('vehicle_details', [])),
                len(fraud.get('fraud_indicators', [])),
                1 if classification.get('severity') in ['high', 'critical'] else 0
            ]
            complexity_score = min(1.0, sum(complexity_factors) / 10.0)
            
            # Generate recommendations
            recommendations = []
            
            if fraud.get('fraud_risk_score', 0.0) > 0.7:
                recommendations.append("Immediate fraud investigation required")
            elif fraud.get('fraud_risk_score', 0.0) > 0.4:
                recommendations.append("Enhanced verification recommended")
            
            if classification.get('severity') == 'critical':
                recommendations.append("Escalate to senior adjuster immediately")
            
            if overall_confidence < 0.6:
                recommendations.append("Request additional documentation")
            
            return {
                'overall_confidence': overall_confidence,
                'complexity_score': complexity_score,
                'processing_priority': self._calculate_priority(classification, fraud),
                'estimated_processing_time': self._estimate_processing_time(complexity_score, fraud.get('fraud_risk_score', 0.0)),
                'recommendations': recommendations,
                'next_steps': self._generate_next_steps(analysis_results),
                'human_review_required': self._requires_human_review(analysis_results)
            }
        
        except Exception as e:
            logger.error(f"Error generating overall analysis: {e}")
            return {
                'overall_confidence': 0.0,
                'complexity_score': 1.0,
                'error': str(e)
            }
    
    def _calculate_priority(self, classification: Dict, fraud: Dict) -> int:
        """Calculate processing priority (1=urgent, 4=low)"""
        if fraud.get('fraud_risk_score', 0.0) > 0.8:
            return 1
        elif classification.get('severity') == 'critical':
            return 1
        elif classification.get('severity') == 'high':
            return 2
        elif fraud.get('fraud_risk_score', 0.0) > 0.5:
            return 2
        else:
            return 3
    
    def _estimate_processing_time(self, complexity: float, fraud_risk: float) -> int:
        """Estimate processing time in minutes"""
        base_time = 30
        complexity_factor = complexity * 60
        fraud_factor = fraud_risk * 120
        
        return int(base_time + complexity_factor + fraud_factor)
    
    def _generate_next_steps(self, analysis_results: Dict) -> List[str]:
        """Generate next steps based on analysis"""
        steps = []
        
        classification = analysis_results.get('classification', {})
        fraud = analysis_results.get('fraud_analysis', {})
        
        if fraud.get('fraud_risk_score', 0.0) > 0.6:
            steps.append("Conduct fraud investigation")
        
        if classification.get('confidence_score', 0.0) < 0.7:
            steps.append("Request additional documentation")
        
        steps.append("Assign to qualified adjuster")
        steps.append("Verify policy coverage")
        steps.append("Contact claimant for clarification if needed")
        
        return steps
    
    def _requires_human_review(self, analysis_results: Dict) -> bool:
        """Determine if human review is required - ALWAYS TRUE"""
        # ALL CLAIMS REQUIRE HUMAN REVIEW - NO AUTO-APPROVAL
        # This ensures proper oversight and compliance for all claims
        return True
    
    async def _update_claim_scores(self, claim_id: str, analysis_results: Dict):
        """Update claim with analysis scores"""
        try:
            overall = analysis_results.get('overall', {})
            fraud = analysis_results.get('fraud_analysis', {})
            classification = analysis_results.get('classification', {})
            
            updates = {
                'ai_confidence_score': overall.get('overall_confidence', 0.0),
                'fraud_risk_score': fraud.get('fraud_risk_score', 0.0),
                'complexity_score': overall.get('complexity_score', 0.0),
                'priority': self._calculate_priority(classification, fraud),
                'estimated_amount': classification.get('estimated_amount'),
                'status': 'human_review' if overall.get('human_review_required') else 'ai_analysis'
            }
            
            await self.supabase.update_claim(claim_id, updates)
            
        except Exception as e:
            logger.error(f"Error updating claim scores: {e}")
