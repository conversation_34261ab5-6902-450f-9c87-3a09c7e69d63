"""
ARIA AI Claim Classifier
Determines if an email is a claim submission
"""

import logging
from typing import Dict, List, Tuple
import re

from ..config import Settings

logger = logging.getLogger(__name__)


class ClaimClassifier:
    """AI-powered claim classification service"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        
        # Keywords that indicate a claim
        self.claim_keywords = [
            'accident', 'claim', 'damage', 'injury', 'collision', 'crash',
            'incident', 'loss', 'theft', 'fire', 'flood', 'vandalism',
            'liability', 'coverage', 'policy', 'deductible', 'settlement',
            'repair', 'medical', 'hospital', 'police report', 'estimate'
        ]
        
        # Keywords that indicate NOT a claim
        self.non_claim_keywords = [
            'quote', 'renewal', 'payment', 'billing', 'general inquiry',
            'question', 'information', 'policy change', 'address change'
        ]
    
    async def is_claim_email(self, subject: str, body: str, attachments: List[Dict]) -> Tuple[bool, float]:
        """
        Determine if an email is a claim submission
        Returns (is_claim, confidence_score)
        """
        try:
            confidence = 0.0
            
            # Check subject line
            subject_lower = subject.lower()
            for keyword in self.claim_keywords:
                if keyword in subject_lower:
                    confidence += 0.3
                    break
            
            # Check body content
            body_lower = body.lower()
            claim_keyword_count = sum(1 for keyword in self.claim_keywords if keyword in body_lower)
            non_claim_keyword_count = sum(1 for keyword in self.non_claim_keywords if keyword in body_lower)
            
            # Adjust confidence based on keywords
            confidence += min(claim_keyword_count * 0.1, 0.4)
            confidence -= min(non_claim_keyword_count * 0.2, 0.3)
            
            # Check for attachments (strong indicator of claim)
            if attachments:
                confidence += 0.4
                
                # Check attachment types
                for attachment in attachments:
                    filename = attachment.get('filename', '').lower()
                    if any(ext in filename for ext in ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']):
                        confidence += 0.1
            
            # Check for specific patterns
            if self._has_claim_patterns(body):
                confidence += 0.2
            
            # Normalize confidence to 0-1 range
            confidence = min(max(confidence, 0.0), 1.0)
            
            # Determine if it's a claim (threshold: 0.5)
            is_claim = confidence >= 0.5
            
            logger.info(f"Email classification: is_claim={is_claim}, confidence={confidence:.2f}")
            return is_claim, confidence
            
        except Exception as e:
            logger.error(f"Error in claim classification: {e}")
            return False, 0.0
    
    def _has_claim_patterns(self, text: str) -> bool:
        """Check for specific claim-related patterns in text"""
        patterns = [
            r'\b(policy\s*#?\s*\w+)',  # Policy number
            r'\b(claim\s*#?\s*\w+)',   # Claim number
            r'\$\d+',                   # Dollar amounts
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # Dates
            r'\b(incident|accident|collision)\s+(on|at|occurred)',  # Incident descriptions
            r'\b(damage|damages?|injured?|hurt)\b',  # Damage/injury terms
        ]
        
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        
        return False
