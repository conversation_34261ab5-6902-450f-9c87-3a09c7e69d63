"""
ARIA AI Claim Classifier
Determines if an email is a claim submission
"""

import logging
from typing import Dict, List, Tuple
import re

from ..config import Settings

logger = logging.getLogger(__name__)


class ClaimClassifier:
    """AI-powered claim classification service"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        
        # Keywords that indicate a claim
        self.claim_keywords = [
            'accident', 'claim', 'damage', 'injury', 'collision', 'crash',
            'incident', 'loss', 'theft', 'fire', 'flood', 'vandalism',
            'liability', 'coverage', 'policy', 'deductible', 'settlement',
            'repair', 'medical', 'hospital', 'police report', 'estimate',
            'broken', 'damaged', 'hurt', 'injured', 'stolen', 'hit',
            'rear-ended', 'fender bender', 'totaled', 'windshield',
            'bumper', 'dent', 'scratch', 'leak', 'water damage',
            'hail damage', 'storm damage', 'tree fell', 'slip and fall',
            'workers comp', 'work injury', 'on the job', 'workplace',
            'need to file', 'filing a claim', 'submit claim', 'claim help',
            'insurance claim', 'need help with claim', 'claim assistance'
        ]

        # Keywords that indicate NOT a claim
        self.non_claim_keywords = [
            'quote', 'renewal', 'payment', 'billing', 'general inquiry',
            'question', 'information', 'policy change', 'address change',
            'premium', 'rate', 'discount', 'coverage options', 'new policy',
            'cancel policy', 'update information', 'phone number change',
            'email change', 'beneficiary change', 'add driver', 'remove driver'
        ]

        # Phrases that strongly indicate a claim request
        self.claim_phrases = [
            'i need to file a claim',
            'i want to submit a claim',
            'i need help with a claim',
            'i was in an accident',
            'my car was damaged',
            'i was injured',
            'something happened to my',
            'i need to report an incident',
            'please help me with my claim',
            'claim assistance needed',
            'filing an insurance claim'
        ]
    
    async def is_claim_email(self, subject: str, body: str, attachments: List[Dict]) -> Tuple[bool, float]:
        """
        Determine if an email is a claim submission
        Returns (is_claim, confidence_score)
        """
        try:
            confidence = 0.0

            # Combine subject and body for analysis
            full_text = f"{subject} {body}".lower()

            # Check for strong claim phrases first (highest confidence)
            for phrase in self.claim_phrases:
                if phrase in full_text:
                    confidence += 0.6
                    logger.info(f"Found strong claim phrase: '{phrase}'")
                    break

            # Check subject line for claim keywords
            subject_lower = subject.lower()
            subject_claim_keywords = sum(1 for keyword in self.claim_keywords if keyword in subject_lower)
            if subject_claim_keywords > 0:
                confidence += min(subject_claim_keywords * 0.2, 0.4)

            # Check body content for keywords
            body_lower = body.lower()
            claim_keyword_count = sum(1 for keyword in self.claim_keywords if keyword in body_lower)
            non_claim_keyword_count = sum(1 for keyword in self.non_claim_keywords if keyword in body_lower)

            # Adjust confidence based on keywords
            confidence += min(claim_keyword_count * 0.08, 0.3)
            confidence -= min(non_claim_keyword_count * 0.15, 0.4)

            # Check for attachments (strong indicator of claim)
            if attachments:
                confidence += 0.3
                logger.info(f"Email has {len(attachments)} attachments - increasing claim confidence")

                # Check attachment types for claim-related documents
                for attachment in attachments:
                    filename = attachment.get('filename', '').lower()
                    if any(ext in filename for ext in ['.pdf', '.jpg', '.jpeg', '.png', '.doc', '.docx']):
                        confidence += 0.05
                    # Look for claim-related filenames
                    if any(term in filename for term in ['police', 'report', 'estimate', 'receipt', 'damage', 'photo']):
                        confidence += 0.1

            # Check for specific claim patterns
            if self._has_claim_patterns(full_text):
                confidence += 0.15

            # Normalize confidence to 0-1 range
            confidence = min(max(confidence, 0.0), 1.0)

            # Determine if it's a claim (threshold: 0.4 for better sensitivity)
            is_claim = confidence >= 0.4

            logger.info(f"Email classification: is_claim={is_claim}, confidence={confidence:.2f}")
            logger.info(f"Subject: '{subject[:50]}...', Body length: {len(body)}, Attachments: {len(attachments)}")

            return is_claim, confidence

        except Exception as e:
            logger.error(f"Error in claim classification: {e}")
            return False, 0.0
    
    def _has_claim_patterns(self, text: str) -> bool:
        """Check for specific claim-related patterns in text"""
        patterns = [
            r'\b(policy\s*#?\s*\w+)',  # Policy number
            r'\b(claim\s*#?\s*\w+)',   # Claim number
            r'\$\d+',                   # Dollar amounts
            r'\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b',  # Dates
            r'\b(incident|accident|collision)\s+(on|at|occurred)',  # Incident descriptions
            r'\b(damage|damages?|injured?|hurt)\b',  # Damage/injury terms
            r'\b(happened\s+(on|at|yesterday|today|last))',  # Time references
            r'\b(need\s+to\s+(file|submit|report))',  # Action phrases
            r'\b(insurance\s+(claim|company|policy))',  # Insurance references
            r'\b(police\s+(report|number|case))',  # Police report references
            r'\b(repair\s+(estimate|cost|shop))',  # Repair references
            r'\b(medical\s+(bill|report|treatment))',  # Medical references
        ]

        pattern_count = 0
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_count += 1

        # Return True if we find multiple patterns (stronger indicator)
        return pattern_count >= 2
