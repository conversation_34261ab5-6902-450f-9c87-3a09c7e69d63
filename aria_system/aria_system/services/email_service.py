"""
Email processing service for ARIA system.
Handles incoming email monitoring, parsing, and outgoing email communication.
"""

import asyncio
import imaplib
import smtplib
import email
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import re

from ..config import settings
from ..utils.logger import get_logger, log_email_event, log_claim_event
from .cloud_services import CloudServicesManager

logger = get_logger(__name__)


class EmailProcessor:
    """Handles email processing for claims."""
    
    def __init__(self, cloud_services: CloudServicesManager):
        self.cloud_services = cloud_services
        self.email_config = settings.email
        self.running = False
    
    async def start_monitoring(self):
        """Start monitoring emails for new claims."""
        self.running = True
        logger.info("Starting email monitoring...")
        
        while self.running:
            try:
                await self._check_new_emails()
                await asyncio.sleep(self.email_config.check_interval)
            except Exception as e:
                logger.error(f"Error in email monitoring: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def stop_monitoring(self):
        """Stop email monitoring."""
        self.running = False
        logger.info("Email monitoring stopped")
    
    async def _check_new_emails(self) -> List[Dict[str, Any]]:
        """Check for new claim emails."""
        try:
            with imaplib.IMAP4_SSL(self.email_config.imap_server, self.email_config.imap_port) as mail:
                mail.login(self.email_config.claims_email, self.email_config.claims_password)
                mail.select('INBOX')
                
                # Search for unread emails
                _, message_numbers = mail.search(None, 'UNSEEN')
                
                if not message_numbers[0]:
                    return []
                
                logger.info(f"Found {len(message_numbers[0].split())} new emails")
                
                processed_emails = []
                for num in message_numbers[0].split():
                    email_data = await self._process_email(mail, num)
                    if email_data:
                        processed_emails.append(email_data)
                        # Mark as read
                        mail.store(num, '+FLAGS', '\\Seen')
                
                return processed_emails
                
        except Exception as e:
            logger.error(f"Error checking emails: {e}")
            return []
    
    async def _process_email(self, mail, message_num) -> Optional[Dict[str, Any]]:
        """Process individual email."""
        try:
            _, msg_data = mail.fetch(message_num, '(RFC822)')
            email_body = msg_data[0][1]
            email_message = email.message_from_bytes(email_body)
            
            # Extract email details
            sender = self._extract_email_address(email_message['From'])
            sender_name = self._extract_sender_name(email_message['From'])
            subject = email_message['Subject'] or "No Subject"
            received_date = email_message['Date']
            body = self._extract_email_body(email_message)
            
            # Generate claim ID
            claim_number = self._generate_claim_number()
            
            logger.info(f"Processing new claim: {claim_number} from {sender}")
            
            # Process attachments
            attachments = await self._process_attachments(email_message, claim_number)
            
            # Extract claim information
            claim_info = self._extract_claim_information(subject, body)
            
            email_data = {
                'claim_number': claim_number,
                'sender_email': sender,
                'sender_name': sender_name,
                'subject': subject,
                'body': body,
                'received_date': received_date,
                'attachments': attachments,
                'claim_info': claim_info,
                'processed_at': datetime.utcnow().isoformat()
            }
            
            log_claim_event(claim_number, "Email received and processed", {
                "sender": sender,
                "attachments_count": len(attachments)
            })
            
            return email_data
            
        except Exception as e:
            logger.error(f"Error processing email: {e}")
            return None
    
    def _extract_email_address(self, from_field: str) -> str:
        """Extract email address from From field."""
        if '<' in from_field and '>' in from_field:
            return from_field.split('<')[1].split('>')[0]
        return from_field.strip()
    
    def _extract_sender_name(self, from_field: str) -> str:
        """Extract sender name from From field."""
        if '<' in from_field:
            return from_field.split('<')[0].strip().strip('"')
        return from_field.strip()
    
    def _extract_email_body(self, email_message) -> str:
        """Extract text body from email."""
        body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                if part.get_content_type() == "text/plain":
                    try:
                        body += part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    except:
                        body += str(part.get_payload())
                elif part.get_content_type() == "text/html" and not body:
                    # Fallback to HTML if no plain text
                    try:
                        html_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        # Simple HTML to text conversion
                        body = re.sub('<[^<]+?>', '', html_content)
                    except:
                        body += str(part.get_payload())
        else:
            try:
                body = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
            except:
                body = str(email_message.get_payload())
        
        return body.strip()
    
    async def _process_attachments(self, email_message, claim_number: str) -> List[Dict[str, Any]]:
        """Process email attachments."""
        attachments = []
        
        for part in email_message.walk():
            if part.get_content_disposition() == 'attachment':
                filename = part.get_filename()
                if filename:
                    try:
                        file_content = part.get_payload(decode=True)
                        
                        # Store in cloud storage
                        storage_urls = await self.cloud_services.store_document(
                            file_content, filename, claim_number
                        )
                        
                        attachment_info = {
                            'filename': filename,
                            'size': len(file_content),
                            'content_type': part.get_content_type(),
                            'storage_urls': storage_urls
                        }
                        
                        attachments.append(attachment_info)
                        logger.info(f"Processed attachment: {filename} for claim {claim_number}")
                        
                    except Exception as e:
                        logger.error(f"Error processing attachment {filename}: {e}")
        
        return attachments
    
    def _extract_claim_information(self, subject: str, body: str) -> Dict[str, Any]:
        """Extract claim information from email content."""
        claim_info = {
            'incident_date': None,
            'incident_location': None,
            'policy_number': None,
            'claim_type': None,
            'estimated_amount': None
        }
        
        text = f"{subject} {body}".lower()
        
        # Extract policy number
        policy_patterns = [
            r'policy\s*(?:number|#)?\s*:?\s*([a-z0-9\-]+)',
            r'pol\s*(?:number|#)?\s*:?\s*([a-z0-9\-]+)',
            r'policy\s+([a-z0-9\-]+)'
        ]
        
        for pattern in policy_patterns:
            match = re.search(pattern, text)
            if match:
                claim_info['policy_number'] = match.group(1).upper()
                break
        
        # Extract dates
        date_patterns = [
            r'(?:incident|accident|occurred|happened)\s+(?:on\s+)?(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})'
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                claim_info['incident_date'] = match.group(1)
                break
        
        # Extract monetary amounts
        amount_patterns = [
            r'\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
            r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*dollars?'
        ]
        
        for pattern in amount_patterns:
            match = re.search(pattern, text)
            if match:
                amount_str = match.group(1).replace(',', '')
                try:
                    claim_info['estimated_amount'] = float(amount_str)
                    break
                except ValueError:
                    pass
        
        # Determine claim type based on keywords
        if any(keyword in text for keyword in ['auto', 'car', 'vehicle', 'collision', 'accident']):
            claim_info['claim_type'] = 'auto_liability'
        elif any(keyword in text for keyword in ['property', 'home', 'house', 'building', 'damage']):
            claim_info['claim_type'] = 'property_damage'
        elif any(keyword in text for keyword in ['injury', 'medical', 'hospital', 'doctor']):
            claim_info['claim_type'] = 'bodily_injury'
        elif any(keyword in text for keyword in ['liability', 'lawsuit', 'legal']):
            claim_info['claim_type'] = 'general_liability'
        
        return claim_info
    
    def _generate_claim_number(self) -> str:
        """Generate unique claim number."""
        import uuid
        timestamp = datetime.now().strftime("%Y%m")
        unique_id = str(uuid.uuid4())[:8].upper()
        return f"CLAIM-{timestamp}-{unique_id}"


class EmailSender:
    """Handles outgoing email communication."""
    
    def __init__(self, cloud_services: CloudServicesManager):
        self.cloud_services = cloud_services
        self.email_config = settings.email
    
    async def send_acknowledgment_email(self, claim_data: Dict[str, Any]) -> bool:
        """Send acknowledgment email to claimant."""
        subject = f"[{claim_data['claim_number']}] Claim Received - Processing Started"
        body = self._create_acknowledgment_email_body(claim_data)
        
        return await self._send_email_multi_provider(
            claim_data['sender_email'],
            subject,
            body,
            "acknowledgment"
        )
    
    async def send_status_update_email(self, claim_data: Dict[str, Any], status: str, details: str = "") -> bool:
        """Send status update email to claimant."""
        subject = f"[{claim_data['claim_number']}] Claim Status Update - {status.title()}"
        body = self._create_status_update_email_body(claim_data, status, details)
        
        return await self._send_email_multi_provider(
            claim_data['sender_email'],
            subject,
            body,
            "status_update"
        )
    
    async def send_decision_email(self, claim_data: Dict[str, Any], decision: Dict[str, Any]) -> bool:
        """Send final decision email to claimant."""
        decision_text = decision.get('coverage_decision', 'Under Review')
        subject = f"[{claim_data['claim_number']}] Claim Decision - {decision_text}"
        body = self._create_decision_email_body(claim_data, decision)
        
        return await self._send_email_multi_provider(
            claim_data['sender_email'],
            subject,
            body,
            "decision"
        )
    
    async def _send_email_multi_provider(self, to_email: str, subject: str, body: str, email_type: str) -> bool:
        """Send email using multiple providers for reliability."""
        success = False
        
        # Try Azure first
        if await self.cloud_services.send_email_azure(to_email, subject, body):
            success = True
            logger.info(f"Email sent via Azure: {email_type} to {to_email}")
        
        # Fallback to AWS if Azure fails
        elif await self.cloud_services.send_email_aws(to_email, subject, body):
            success = True
            logger.info(f"Email sent via AWS: {email_type} to {to_email}")
        
        # Fallback to SMTP if cloud providers fail
        elif await self._send_email_smtp(to_email, subject, body):
            success = True
            logger.info(f"Email sent via SMTP: {email_type} to {to_email}")
        
        if not success:
            logger.error(f"Failed to send {email_type} email to {to_email}")
        
        return success
    
    async def _send_email_smtp(self, to_email: str, subject: str, body: str) -> bool:
        """Send email using SMTP as fallback."""
        try:
            msg = MIMEMultipart('alternative')
            msg['From'] = self.email_config.claims_email
            msg['To'] = to_email
            msg['Subject'] = subject
            
            # Add HTML body
            html_part = MIMEText(body, 'html')
            msg.attach(html_part)
            
            # Send email
            with smtplib.SMTP(self.email_config.smtp_server, self.email_config.smtp_port) as server:
                server.starttls()
                server.login(self.email_config.claims_email, self.email_config.claims_password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            logger.error(f"SMTP email error: {e}")
            return False
    
    def _create_acknowledgment_email_body(self, claim_data: Dict[str, Any]) -> str:
        """Create acknowledgment email body."""
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h1 style="margin: 0;">🤖 ARIA Claims Processing</h1>
                    <p style="margin: 5px 0 0 0;">Autonomous Risk Intelligence Agent</p>
                </div>
                
                <h2>Dear {claim_data.get('sender_name', 'Valued Customer')},</h2>
                
                <p>Thank you for submitting your insurance claim. Our AI-powered system has received your request and is already processing it.</p>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">📋 Claim Details:</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li><strong>Claim Number:</strong> {claim_data['claim_number']}</li>
                        <li><strong>Received:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M')}</li>
                        <li><strong>Status:</strong> Processing</li>
                        <li><strong>Documents:</strong> {len(claim_data.get('attachments', []))} files received</li>
                    </ul>
                </div>
                
                <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">🔗 Track Your Claim:</h3>
                    <p><a href="http://{settings.app.dashboard_host}:{settings.app.dashboard_port}/track/{claim_data['claim_number']}" style="color: #2a5298; text-decoration: none; font-weight: bold;">Click here to track your claim status</a></p>
                </div>
                
                <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">⏱️ What Happens Next:</h3>
                    <ol>
                        <li>✅ <strong>Document Verification</strong> (2-5 minutes)</li>
                        <li>🤖 <strong>AI Analysis</strong> (15-30 minutes)</li>
                        <li>👨‍💼 <strong>Expert Review</strong> (if needed)</li>
                        <li>📋 <strong>Final Decision</strong> (30-60 minutes total)</li>
                    </ol>
                </div>
                
                <p><strong>Expected Processing Time:</strong> 30-60 minutes</p>
                <p>We'll send you updates as we progress through each stage.</p>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="margin: 0; color: #6c757d; font-size: 14px;">
                        Best regards,<br>
                        ARIA Claims Team<br>
                        Powered by Zurich Insurance
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _create_status_update_email_body(self, claim_data: Dict[str, Any], status: str, details: str) -> str:
        """Create status update email body."""
        status_colors = {
            'processing': '#ffc107',
            'ai_analysis': '#17a2b8',
            'human_review': '#fd7e14',
            'approved': '#28a745',
            'denied': '#dc3545'
        }
        
        color = status_colors.get(status, '#6c757d')
        
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: {color}; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                    <h1 style="margin: 0;">Claim Status Update</h1>
                    <h2 style="margin: 10px 0 0 0;">{status.replace('_', ' ').title()}</h2>
                </div>
                
                <h2>Dear {claim_data.get('sender_name', 'Valued Customer')},</h2>
                
                <p>We have an update on your claim <strong>{claim_data['claim_number']}</strong>.</p>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">📋 Current Status:</h3>
                    <p><strong>{status.replace('_', ' ').title()}</strong></p>
                    {f'<p>{details}</p>' if details else ''}
                </div>
                
                <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">🔗 Track Your Claim:</h3>
                    <p><a href="http://{settings.app.dashboard_host}:{settings.app.dashboard_port}/track/{claim_data['claim_number']}" style="color: #2a5298; text-decoration: none; font-weight: bold;">View detailed status</a></p>
                </div>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="margin: 0; color: #6c757d; font-size: 14px;">
                        Best regards,<br>
                        ARIA Claims Team
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def _create_decision_email_body(self, claim_data: Dict[str, Any], decision: Dict[str, Any]) -> str:
        """Create decision email body."""
        coverage_decision = decision.get('coverage_decision', 'Under Review')
        settlement_amount = decision.get('settlement_amount', 0)
        
        decision_colors = {
            'covered': '#28a745',
            'not_covered': '#dc3545',
            'partially_covered': '#ffc107',
            'requires_investigation': '#fd7e14'
        }
        
        color = decision_colors.get(coverage_decision.lower(), '#6c757d')
        
        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="background: {color}; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center;">
                    <h1 style="margin: 0;">Claim Decision</h1>
                    <h2 style="margin: 10px 0 0 0;">{coverage_decision.replace('_', ' ').title()}</h2>
                    {f'<h3 style="margin: 10px 0 0 0;">Settlement: ${settlement_amount:,.2f}</h3>' if settlement_amount > 0 else ''}
                </div>
                
                <h2>Dear {claim_data.get('sender_name', 'Valued Customer')},</h2>
                
                <p>We have completed the processing of your insurance claim <strong>{claim_data['claim_number']}</strong>.</p>
                
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">📋 Decision Summary:</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li><strong>Decision:</strong> {coverage_decision.replace('_', ' ').title()}</li>
                        <li><strong>Processing Time:</strong> {decision.get('processing_time', 'N/A')}</li>
                        <li><strong>Decision Date:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M')}</li>
                        {f'<li><strong>Settlement Amount:</strong> ${settlement_amount:,.2f}</li>' if settlement_amount > 0 else ''}
                    </ul>
                </div>
                
                {f'<div style="background: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;"><h3 style="margin-top: 0;">💰 Settlement Details:</h3><p>{decision.get("settlement_details", "Settlement details will be provided separately.")}</p></div>' if settlement_amount > 0 else ''}
                
                <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
                    <h3 style="margin-top: 0;">📞 Next Steps:</h3>
                    <p>If you have any questions about this decision, please contact our customer service team at 1-800-ZURICH or reply to this email.</p>
                </div>
                
                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6;">
                    <p style="margin: 0; color: #6c757d; font-size: 14px;">
                        Thank you for choosing Zurich Insurance.<br>
                        ARIA Claims Team<br>
                        Powered by AI, Guided by Experts
                    </p>
                </div>
            </div>
        </body>
        </html>
        """


class EmailService:
    """Main email service combining processor and sender."""
    
    def __init__(self, cloud_services: CloudServicesManager):
        self.processor = EmailProcessor(cloud_services)
        self.sender = EmailSender(cloud_services)
    
    async def start_monitoring(self):
        """Start email monitoring."""
        await self.processor.start_monitoring()
    
    def stop_monitoring(self):
        """Stop email monitoring."""
        self.processor.stop_monitoring()
    
    async def check_new_emails(self) -> List[Dict[str, Any]]:
        """Check for new emails."""
        return await self.processor._check_new_emails()
    
    async def send_acknowledgment(self, claim_data: Dict[str, Any]) -> bool:
        """Send acknowledgment email."""
        return await self.sender.send_acknowledgment_email(claim_data)
    
    async def send_status_update(self, claim_data: Dict[str, Any], status: str, details: str = "") -> bool:
        """Send status update email."""
        return await self.sender.send_status_update_email(claim_data, status, details)
    
    async def send_decision(self, claim_data: Dict[str, Any], decision: Dict[str, Any]) -> bool:
        """Send decision email."""
        return await self.sender.send_decision_email(claim_data, decision)
