"""
ARIA Multi-Channel Notification Service
Unified notification system using HumanLayer for Email, SMS, Slack
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

import aiohttp
from humanlayer import HumanLayer

from ..config import Settings
from ..database.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)


class NotificationService:
    """Multi-channel notification service using HumanLayer"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)

        # Initialize HumanLayer
        self.humanlayer = HumanLayer(
            api_key=settings.ai.humanlayer_api_key,
            run_id=settings.ai.humanlayer_run_id
        )

        # Initialize template engine
        self._init_templates()

        # Initialize notification channels
        self._init_humanlayer_channels()
    
    def _init_humanlayer_channels(self):
        """Initialize HumanLayer notification channels based on configuration"""
        try:
            # Check which channels are enabled in configuration
            self.email_enabled = getattr(self.settings.notifications, 'enable_email_notifications', True)
            self.sms_enabled = getattr(self.settings.notifications, 'enable_sms_notifications', False)
            self.slack_enabled = getattr(self.settings.notifications, 'enable_slack_notifications', True)
            self.whatsapp_enabled = getattr(self.settings.notifications, 'enable_whatsapp_notifications', False)

            enabled_channels = []
            if self.email_enabled: enabled_channels.append("Email")
            if self.slack_enabled: enabled_channels.append("Slack")
            if self.sms_enabled: enabled_channels.append("SMS")
            if self.whatsapp_enabled: enabled_channels.append("WhatsApp")

            logger.info(f"HumanLayer notification channels initialized: {', '.join(enabled_channels)}")

        except Exception as e:
            logger.warning(f"HumanLayer service not available: {e}")
            self.email_enabled = False
            self.sms_enabled = False
            self.slack_enabled = False
            self.whatsapp_enabled = False
    
    def _init_templates(self):
        """Initialize Jinja2 template engine"""
        template_dir = Path(__file__).parent.parent / "templates" / "notifications"
        template_dir.mkdir(parents=True, exist_ok=True)
        
        self.template_env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=True
        )
        
        # Create default templates if they don't exist
        self._create_default_templates(template_dir)
    
    def _create_default_templates(self, template_dir: Path):
        """Create default notification templates"""
        templates = {
            "claim_received_email.html": """
            <h2>Claim Received - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            <p>We have received your insurance claim and are processing it immediately.</p>
            <p><strong>Claim Reference:</strong> {{ claim_number }}</p>
            <p><strong>Status:</strong> Documents received and under verification</p>
            <p><a href="{{ tracking_link }}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Track Your Claim</a></p>
            <p>You will receive updates at each step of the process.</p>
            <p>Best regards,<br>ARIA Claims Team</p>
            """,
            
            "claim_received_sms.txt": """
            ARIA Claims: Your claim {{ claim_number }} has been received. Track progress: {{ tracking_link }}
            """,
            
            "claim_received_whatsapp.txt": """
            ✅ Your insurance claim has been received!
            
            📋 Reference: {{ claim_number }}
            🔍 Status: Under verification
            📱 Track: {{ tracking_link }}
            
            You'll get updates at each step.
            """,
            
            "agent_assignment_email.html": """
            <h2>New Claim Assignment - {{ claim_number }}</h2>
            <p>Hello {{ agent_name }},</p>
            <p>A new claim has been assigned to you for review.</p>
            <p><strong>Claim:</strong> {{ claim_number }}</p>
            <p><strong>Customer:</strong> {{ customer_name }} ({{ customer_email }})</p>
            <p><strong>Priority:</strong> {{ priority }}</p>
            <p><strong>AI Confidence:</strong> {{ ai_confidence }}%</p>
            <p><a href="{{ review_link }}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Claim</a></p>
            <p>Zendesk Ticket: <a href="{{ zendesk_url }}">{{ zendesk_ticket_id }}</a></p>
            """,
            
            "status_update_email.html": """
            <h2>Claim Update - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            <p>Your claim status has been updated:</p>
            <p><strong>New Status:</strong> {{ new_status }}</p>
            <p><strong>Update:</strong> {{ update_message }}</p>
            {% if eta %}
            <p><strong>Expected completion:</strong> {{ eta }}</p>
            {% endif %}
            <p><a href="{{ tracking_link }}">View Full Details</a></p>
            """,
            
            "approval_decision_email.html": """
            <h2>Claim Decision - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            {% if decision == 'approved' %}
            <p style="color: green;"><strong>✅ Your claim has been APPROVED</strong></p>
            <p><strong>Approved Amount:</strong> ${{ approved_amount }}</p>
            <p>Payment will be processed within 3-5 business days.</p>
            {% elif decision == 'denied' %}
            <p style="color: red;"><strong>❌ Your claim has been DENIED</strong></p>
            <p><strong>Reason:</strong> {{ denial_reason }}</p>
            <p>You have the right to appeal this decision.</p>
            {% endif %}
            <p><a href="{{ tracking_link }}">View Complete Details</a></p>
            """
        }
        
        for filename, content in templates.items():
            template_file = template_dir / filename
            if not template_file.exists():
                template_file.write_text(content.strip())
    
    async def send_multi_channel_notification(
        self,
        user_email: str,
        user_phone: Optional[str],
        message_type: str,
        context: Dict[str, Any],
        channels: List[str] = None
    ):
        """Send notification across enabled channels using HumanLayer"""
        # Determine which channels to use based on configuration
        if channels is None:
            channels = []
            if self.email_enabled:
                channels.append('email')
            if self.slack_enabled:
                channels.append('slack')
            # SMS and WhatsApp are disabled

        # Filter out disabled channels
        enabled_channels = []
        for channel in channels:
            if channel == 'email' and self.email_enabled:
                enabled_channels.append(channel)
            elif channel == 'slack' and self.slack_enabled:
                enabled_channels.append(channel)
            elif channel == 'sms' and self.sms_enabled and user_phone:
                enabled_channels.append(channel)
            elif channel == 'whatsapp' and self.whatsapp_enabled and user_phone:
                enabled_channels.append(channel)

        results = {}

        try:
            # Prepare notification content
            notification_content = await self._prepare_notification_content(message_type, context)

            # Send via enabled channels only
            if 'email' in enabled_channels:
                email_result = await self._send_humanlayer_email(
                    user_email, notification_content, context
                )
                results['email'] = email_result

            if 'slack' in enabled_channels:
                slack_result = await self._send_humanlayer_slack(
                    notification_content, context
                )
                results['slack'] = slack_result

            # SMS and WhatsApp are disabled - skip these channels
            if 'sms' in channels and not self.sms_enabled:
                results['sms'] = 'disabled'
                logger.info("SMS notifications are disabled")

            if 'whatsapp' in channels and not self.whatsapp_enabled:
                results['whatsapp'] = 'disabled'
                logger.info("WhatsApp notifications are disabled")

            # Log notification results
            await self._log_notifications(user_email, message_type, results, context)

            return results

        except Exception as e:
            logger.error(f"Multi-channel notification failed: {e}")
            return {'error': str(e)}
    
    async def _send_humanlayer_email(
        self,
        to_email: str,
        content: Dict[str, str],
        context: Dict[str, Any]
    ) -> bool:
        """Send email notification via HumanLayer"""
        try:
            # Use HumanLayer's human-as-tool for email sending
            email_message = f"""
            Send email notification to {to_email} regarding claim {context.get('claim_number', 'N/A')}

            Subject: {content.get('subject', 'Claim Notification')}

            Email Body:
            {content.get('email_body', 'Claim notification content')}

            Context: {context}
            """

            # Send via HumanLayer
            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(email_message)

            logger.info(f"Email notification sent via HumanLayer to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Error sending email via HumanLayer to {to_email}: {e}")
            return False

    async def _send_humanlayer_sms(
        self,
        to_phone: str,
        content: Dict[str, str],
        context: Dict[str, Any]
    ) -> bool:
        """Send SMS notification via HumanLayer"""
        try:
            sms_message = f"""
            Send SMS notification to {to_phone} regarding claim {context.get('claim_number', 'N/A')}

            SMS Message:
            {content.get('sms_body', 'Claim update notification')}

            Context: {context}
            """

            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(sms_message)

            logger.info(f"SMS notification sent via HumanLayer to {to_phone}")
            return True

        except Exception as e:
            logger.error(f"Error sending SMS via HumanLayer to {to_phone}: {e}")
            return False

    async def _send_humanlayer_slack(
        self,
        content: Dict[str, str],
        context: Dict[str, Any]
    ) -> bool:
        """Send Slack notification via HumanLayer"""
        try:
            slack_message = f"""
            Send Slack notification regarding claim {context.get('claim_number', 'N/A')}

            Channel: {context.get('slack_channel', '#claims-processing')}

            Message:
            {content.get('slack_body', 'Claim notification')}

            Attachments: {content.get('slack_attachments', [])}

            Context: {context}
            """

            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(slack_message)

            logger.info(f"Slack notification sent via HumanLayer")
            return True

        except Exception as e:
            logger.error(f"Error sending Slack notification via HumanLayer: {e}")
            return False
    
    async def _prepare_notification_content(
        self,
        message_type: str,
        context: Dict[str, Any]
    ) -> Dict[str, str]:
        """Prepare notification content for all channels"""
        try:
            content = {}

            # Generate email content
            if self._template_exists(f"{message_type}_email.html"):
                email_template = self.template_env.get_template(f"{message_type}_email.html")
                content['email_body'] = email_template.render(context)
                content['subject'] = self._generate_email_subject(message_type, context)

            # Generate SMS content
            if self._template_exists(f"{message_type}_sms.txt"):
                sms_template = self.template_env.get_template(f"{message_type}_sms.txt")
                content['sms_body'] = sms_template.render(context)

            # Generate Slack content
            content['slack_body'] = self._generate_slack_message(message_type, context)
            content['slack_attachments'] = self._generate_slack_attachments(message_type, context)

            return content

        except Exception as e:
            logger.error(f"Error preparing notification content: {e}")
            return {
                'email_body': f"Notification: {message_type}",
                'sms_body': f"Update: {context.get('claim_number', 'Your claim')}",
                'slack_body': f"Claim update: {context.get('claim_number', 'N/A')}",
                'subject': f"Claim Notification - {context.get('claim_number', 'N/A')}"
            }
    
    def _template_exists(self, template_name: str) -> bool:
        """Check if template exists"""
        try:
            self.template_env.get_template(template_name)
            return True
        except:
            return False

    def _generate_slack_message(self, message_type: str, context: Dict[str, Any]) -> str:
        """Generate Slack message content"""
        claim_number = context.get('claim_number', 'N/A')

        if message_type == 'claim_received':
            return f"🚨 New claim received: {claim_number}\nCustomer: {context.get('user_name', 'N/A')}\nStatus: Processing started"
        elif message_type == 'status_update':
            return f"📋 Claim update: {claim_number}\nNew status: {context.get('new_status', 'N/A')}\nMessage: {context.get('update_message', 'N/A')}"
        elif message_type == 'agent_assignment':
            return f"👥 Claim assigned: {claim_number}\nAgent: {context.get('agent_name', 'N/A')}\nPriority: {context.get('priority', 'Normal')}"
        else:
            return f"📢 Claim notification: {claim_number}"

    def _generate_slack_attachments(self, message_type: str, context: Dict[str, Any]) -> List[Dict]:
        """Generate Slack message attachments"""
        attachments = []

        if message_type == 'claim_received':
            attachments.append({
                "color": "good",
                "fields": [
                    {"title": "Claim Number", "value": context.get('claim_number', 'N/A'), "short": True},
                    {"title": "Customer", "value": context.get('user_name', 'N/A'), "short": True},
                    {"title": "Amount", "value": f"${context.get('claimed_amount', 'TBD')}", "short": True},
                    {"title": "Priority", "value": context.get('priority', 'Normal'), "short": True}
                ]
            })

        return attachments
    
    async def send_slack_notification(
        self,
        channel: str,
        message: str,
        attachments: List[Dict] = None,
        blocks: List[Dict] = None
    ) -> bool:
        """Send Slack notification"""
        try:
            webhook_url = self.settings.slack.webhook_url
            
            payload = {
                "channel": channel,
                "text": message,
                "username": "ARIA Claims Bot",
                "icon_emoji": ":robot_face:"
            }
            
            if attachments:
                payload["attachments"] = attachments
            
            if blocks:
                payload["blocks"] = blocks
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Slack notification sent to {channel}")
                        return True
                    else:
                        logger.error(f"Slack notification failed: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
            return False
    
    def _generate_email_subject(self, template: str, context: Dict) -> str:
        """Generate email subject based on template and context"""
        subjects = {
            "claim_received": f"Claim Received - {context.get('claim_number', 'New Claim')}",
            "status_update": f"Claim Update - {context.get('claim_number', 'Your Claim')}",
            "agent_assignment": f"New Claim Assignment - {context.get('claim_number', '')}",
            "approval_decision": f"Claim Decision - {context.get('claim_number', 'Your Claim')}",
            "request_attachments": "Additional Documents Required for Your Claim",
            "claim_guidance": "How to Submit Your Insurance Claim"
        }
        
        template_key = template.replace("_email", "")
        return subjects.get(template_key, "ARIA Claims Notification")
    
    async def _log_notifications(
        self,
        recipient: str,
        message_type: str,
        results: Dict,
        context: Dict
    ):
        """Log notification results to database"""
        try:
            for channel, success in results.items():
                notification_data = {
                    'recipient': recipient,
                    'channel': channel,
                    'message_type': message_type,
                    'status': 'sent' if success else 'failed',
                    'context': context,
                    'sent_at': datetime.now() if success else None
                }
                
                await self.supabase.log_notification(notification_data)
        
        except Exception as e:
            logger.error(f"Error logging notifications: {e}")
