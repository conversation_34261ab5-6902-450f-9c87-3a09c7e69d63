"""
Main ARIA application orchestrator.
Coordinates email processing, AI analysis, human interactions, and decision communication.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import uuid4

from ..config import settings
from ..database import get_async_db, init_database
from ..models.claims import <PERSON>laim, ClaimStatus, ClaimType, CoverageDecision
from ..models.documents import Document, DocumentStatus
from ..models.human_interactions import HumanInteraction, ExpertType, InteractionStatus
from ..models.audit_logs import AuditLog, AuditAction
from ..services.cloud_services import CloudServicesManager
from ..services.email_service import EmailService
from ..services.human_interaction_service import HumanInteractionService
from ..utils.logger import get_logger, log_claim_event, log_ai_decision

logger = get_logger(__name__)


class ARIAApplication:
    """Main ARIA application orchestrating the complete claims processing workflow."""
    
    def __init__(self):
        self.cloud_services = CloudServicesManager()
        self.email_service = EmailService(self.cloud_services)
        self.human_service = HumanInteractionService()
        self.running = False
        
        logger.info("ARIA Application initialized")
    
    async def start(self):
        """Start the ARIA application."""
        logger.info("Starting ARIA Application...")
        
        # Initialize database
        await init_database()
        
        # Start background tasks
        self.running = True
        
        # Start email monitoring
        email_task = asyncio.create_task(self._email_monitoring_loop())
        
        # Start human interaction monitoring
        interaction_task = asyncio.create_task(self._interaction_monitoring_loop())
        
        logger.info("ARIA Application started successfully")
        
        try:
            # Wait for tasks to complete
            await asyncio.gather(email_task, interaction_task)
        except KeyboardInterrupt:
            logger.info("Shutting down ARIA Application...")
            await self.stop()
    
    async def stop(self):
        """Stop the ARIA application."""
        self.running = False
        self.email_service.stop_monitoring()
        logger.info("ARIA Application stopped")
    
    async def _email_monitoring_loop(self):
        """Monitor emails and process new claims."""
        while self.running:
            try:
                # Check for new emails
                new_emails = await self.email_service.check_new_emails()
                
                for email_data in new_emails:
                    # Process each email as a new claim
                    await self._process_new_claim(email_data)
                
                # Wait before checking again
                await asyncio.sleep(settings.email.check_interval)
                
            except Exception as e:
                logger.error(f"Error in email monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _interaction_monitoring_loop(self):
        """Monitor human interactions and handle overdue cases."""
        while self.running:
            try:
                # Check for overdue interactions
                overdue = await self.human_service.check_overdue_interactions()
                
                for interaction in overdue:
                    await self._handle_overdue_interaction(interaction)
                
                # Wait before checking again
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in interaction monitoring loop: {e}")
                await asyncio.sleep(300)
    
    async def _process_new_claim(self, email_data: Dict[str, Any]):
        """Process a new claim from email data."""
        try:
            claim_number = email_data['claim_number']
            
            log_claim_event(claim_number, "Processing started", {
                "sender": email_data['sender_email'],
                "attachments": len(email_data.get('attachments', []))
            })
            
            # Create claim record
            claim = await self._create_claim_record(email_data)
            
            # Send acknowledgment email
            await self.email_service.send_acknowledgment(email_data)
            
            # Process documents
            await self._process_claim_documents(claim, email_data.get('attachments', []))
            
            # Run AI analysis
            ai_result = await self._run_ai_analysis(claim)
            
            # Handle decision based on AI result
            await self._handle_ai_decision(claim, ai_result)
            
            log_claim_event(claim_number, "Processing completed", {
                "decision": ai_result.get('coverage_decision'),
                "requires_human_review": ai_result.get('requires_human_review', False)
            })
            
        except Exception as e:
            logger.error(f"Error processing claim {email_data.get('claim_number', 'unknown')}: {e}")
    
    async def _create_claim_record(self, email_data: Dict[str, Any]) -> Claim:
        """Create a new claim record in the database."""
        async with get_async_db() as db:
            claim = Claim(
                claim_number=email_data['claim_number'],
                claimant_name=email_data['sender_name'],
                claimant_email=email_data['sender_email'],
                subject=email_data['subject'],
                description=email_data['body'],
                status=ClaimStatus.RECEIVED,
                processing_start=datetime.utcnow().isoformat(),
                
                # Extract claim info if available
                claim_type=self._map_claim_type(email_data.get('claim_info', {}).get('claim_type')),
                policy_number=email_data.get('claim_info', {}).get('policy_number'),
                claimed_amount=email_data.get('claim_info', {}).get('estimated_amount'),
            )
            
            db.add(claim)
            await db.commit()
            await db.refresh(claim)
            
            # Create audit log
            audit_log = AuditLog.create_log(
                action=AuditAction.CLAIM_CREATED,
                entity_type="claim",
                entity_id=str(claim.id),
                description=f"Claim {claim.claim_number} created from email",
                actor_type="system",
                actor_name="email_processor",
                details={
                    "sender": email_data['sender_email'],
                    "subject": email_data['subject']
                }
            )
            db.add(audit_log)
            await db.commit()
            
            return claim
    
    async def _process_claim_documents(self, claim: Claim, attachments: List[Dict[str, Any]]):
        """Process claim documents and store them."""
        async with get_async_db() as db:
            for attachment in attachments:
                document = Document(
                    claim_id=claim.id,
                    filename=attachment['filename'],
                    original_filename=attachment['filename'],
                    file_extension=attachment['filename'].split('.')[-1] if '.' in attachment['filename'] else '',
                    file_size=attachment['size'],
                    mime_type=attachment.get('content_type'),
                    status=DocumentStatus.UPLOADED
                )
                
                # Set cloud URLs
                document.set_cloud_urls(attachment.get('storage_urls', {}))
                
                # Classify document type
                document.document_type = document.classify_document_type()
                
                db.add(document)
                
                # Process document with cloud services
                await self._process_document_content(document, attachment['storage_urls'])
            
            await db.commit()
    
    async def _process_document_content(self, document: Document, storage_urls: Dict[str, str]):
        """Process document content using cloud AI services."""
        try:
            # Try multiple providers for document processing
            processing_result = None
            
            # Try Azure first
            if 'azure' in storage_urls:
                processing_result = await self.cloud_services.process_document_azure(
                    storage_urls['azure'], str(document.id)
                )
            
            # Fallback to AWS
            if not processing_result or processing_result.get('status') == 'error':
                if 'aws' in storage_urls:
                    s3_parts = storage_urls['aws'].replace('s3://', '').split('/')
                    bucket = s3_parts[0]
                    key = '/'.join(s3_parts[1:])
                    processing_result = await self.cloud_services.process_document_aws(
                        bucket, key, str(document.id)
                    )
            
            # Fallback to GCP
            if not processing_result or processing_result.get('status') == 'error':
                if 'gcp' in storage_urls:
                    processing_result = await self.cloud_services.process_document_gcp(
                        storage_urls['gcp'], str(document.id)
                    )
            
            # Update document with processing results
            if processing_result and processing_result.get('status') == 'success':
                document.extracted_text = processing_result.get('text', '')
                document.ocr_confidence = processing_result.get('confidence', '0%')
                document.processing_provider = processing_result.get('provider')
                document.status = DocumentStatus.PROCESSED
            else:
                document.status = DocumentStatus.FAILED
                document.processing_error = processing_result.get('error', 'Unknown error') if processing_result else 'No processing result'
                
        except Exception as e:
            logger.error(f"Error processing document {document.filename}: {e}")
            document.status = DocumentStatus.FAILED
            document.processing_error = str(e)
    
    async def _run_ai_analysis(self, claim: Claim) -> Dict[str, Any]:
        """Run AI analysis on the claim."""
        try:
            # Simulate AI analysis for demo purposes
            # In production, this would use the actual AI agent
            
            ai_result = await self._simulate_ai_analysis(claim)
            
            # Update claim with AI results
            async with get_async_db() as db:
                claim.coverage_decision = CoverageDecision(ai_result['coverage_decision'])
                claim.coverage_rationale = ai_result['coverage_rationale']
                claim.fault_percentage = ai_result['fault_percentage']
                claim.confidence_score = ai_result['confidence_score']
                claim.estimated_value = ai_result['estimated_settlement']
                claim.complexity_level = ai_result['complexity_level']
                claim.requires_human_review = ai_result['requires_human_review']
                claim.status = ClaimStatus.AI_ANALYSIS
                
                # Set risk factors
                claim.set_risk_factors(ai_result.get('risk_factors', []))
                claim.set_fraud_indicators(ai_result.get('fraud_indicators', []))
                
                await db.commit()
                
                # Create audit log
                audit_log = AuditLog.create_log(
                    action=AuditAction.AI_ANALYSIS_COMPLETED,
                    entity_type="claim",
                    entity_id=str(claim.id),
                    claim_id=str(claim.id),
                    description=f"AI analysis completed for claim {claim.claim_number}",
                    actor_type="ai",
                    actor_name="aria_ai_agent",
                    details=ai_result
                )
                db.add(audit_log)
                await db.commit()
            
            log_ai_decision(
                claim.claim_number,
                ai_result['coverage_decision'],
                ai_result['confidence_score'],
                {"complexity": ai_result['complexity_level']}
            )
            
            return ai_result
            
        except Exception as e:
            logger.error(f"Error in AI analysis for claim {claim.claim_number}: {e}")
            return {
                'coverage_decision': 'requires_investigation',
                'confidence_score': 0.0,
                'requires_human_review': True,
                'error': str(e)
            }
    
    async def _simulate_ai_analysis(self, claim: Claim) -> Dict[str, Any]:
        """Simulate AI analysis for demo purposes."""
        # Simulate processing time
        await asyncio.sleep(2)
        
        # Determine complexity based on claim amount and type
        estimated_value = claim.claimed_amount or 25000
        confidence = 0.85 if estimated_value < 30000 else 0.65
        
        # Determine if human review is needed
        requires_human_review = (
            estimated_value > 50000 or
            confidence < 0.7 or
            claim.claim_type in [ClaimType.BODILY_INJURY, ClaimType.PROFESSIONAL_LIABILITY]
        )
        
        return {
            'coverage_decision': 'covered' if confidence > 0.7 else 'requires_investigation',
            'coverage_rationale': f"Based on policy analysis and incident description, this {claim.claim_type or 'liability'} claim appears to be covered under the standard policy terms.",
            'fault_percentage': 75.0,
            'confidence_score': confidence,
            'estimated_settlement': int(estimated_value * 0.8),
            'complexity_level': 'high' if estimated_value > 50000 else 'medium' if estimated_value > 20000 else 'low',
            'requires_human_review': requires_human_review,
            'risk_factors': ['Standard incident type', 'Adequate documentation provided'],
            'fraud_indicators': [],
            'processing_time_minutes': 2
        }
    
    async def _handle_ai_decision(self, claim: Claim, ai_result: Dict[str, Any]):
        """Handle the AI decision and determine next steps."""
        try:
            if ai_result.get('requires_human_review'):
                # Route to human expert
                await self._request_human_review(claim, ai_result)
            else:
                # Auto-approve if confidence is high
                await self._auto_approve_claim(claim, ai_result)
                
        except Exception as e:
            logger.error(f"Error handling AI decision for claim {claim.claim_number}: {e}")
    
    async def _request_human_review(self, claim: Claim, ai_result: Dict[str, Any]):
        """Request human expert review for the claim."""
        try:
            # Determine appropriate expert type
            expert_type = self._determine_expert_type(claim, ai_result)
            
            # Create context for human expert
            context = {
                'claim_id': claim.claim_number,
                'claim_type': claim.claim_type.value if claim.claim_type else 'unknown',
                'estimated_value': ai_result.get('estimated_settlement', 0),
                'ai_confidence': ai_result.get('confidence_score', 0),
                'coverage_decision': ai_result.get('coverage_decision'),
                'complexity_level': ai_result.get('complexity_level')
            }
            
            # Contact human expert
            request = f"""
            Claim {claim.claim_number} requires expert review.
            
            AI Analysis Summary:
            - Coverage Decision: {ai_result.get('coverage_decision', 'Unknown')}
            - Confidence: {ai_result.get('confidence_score', 0):.1%}
            - Estimated Value: ${ai_result.get('estimated_settlement', 0):,}
            - Complexity: {ai_result.get('complexity_level', 'Unknown')}
            
            Please review and provide your expert assessment.
            """
            
            urgency = 'urgent' if claim.estimated_value and claim.estimated_value > 100000 else 'high' if claim.estimated_value and claim.estimated_value > 50000 else 'normal'
            
            interaction_result = await self.human_service.contact_expert(
                expert_type=expert_type,
                request=request,
                context=context,
                urgency=urgency,
                claim_id=claim.claim_number
            )
            
            # Update claim status
            async with get_async_db() as db:
                claim.status = ClaimStatus.HUMAN_REVIEW
                await db.commit()
            
            log_claim_event(claim.claim_number, f"Human review requested from {expert_type.value}", {
                "interaction_id": interaction_result.get('interaction_id'),
                "urgency": urgency
            })
            
        except Exception as e:
            logger.error(f"Error requesting human review for claim {claim.claim_number}: {e}")
    
    async def _auto_approve_claim(self, claim: Claim, ai_result: Dict[str, Any]):
        """Auto-approve claim based on AI analysis."""
        try:
            async with get_async_db() as db:
                claim.status = ClaimStatus.APPROVED
                claim.auto_approved = True
                claim.settlement_amount = ai_result.get('estimated_settlement')
                claim.processing_end = datetime.utcnow().isoformat()
                await db.commit()
            
            # Send decision email
            decision_data = {
                'coverage_decision': ai_result.get('coverage_decision'),
                'settlement_amount': ai_result.get('estimated_settlement'),
                'processing_time': claim.processing_time_minutes
            }
            
            claim_data = {
                'claim_number': claim.claim_number,
                'sender_email': claim.claimant_email,
                'sender_name': claim.claimant_name
            }
            
            await self.email_service.send_decision(claim_data, decision_data)
            
            log_claim_event(claim.claim_number, "Auto-approved", {
                "settlement_amount": ai_result.get('estimated_settlement'),
                "confidence": ai_result.get('confidence_score')
            })
            
        except Exception as e:
            logger.error(f"Error auto-approving claim {claim.claim_number}: {e}")
    
    def _determine_expert_type(self, claim: Claim, ai_result: Dict[str, Any]) -> ExpertType:
        """Determine appropriate expert type based on claim and AI analysis."""
        estimated_value = ai_result.get('estimated_settlement', 0)
        confidence = ai_result.get('confidence_score', 1.0)
        
        # High-value claims go to manager
        if estimated_value > 50000:
            return ExpertType.MANAGER_APPROVER
        
        # Low confidence cases go to senior adjuster
        if confidence < 0.7:
            return ExpertType.SENIOR_ADJUSTER
        
        # Fraud indicators go to fraud investigator
        if ai_result.get('fraud_indicators'):
            return ExpertType.FRAUD_INVESTIGATOR
        
        # Complex cases go to senior adjuster
        if ai_result.get('complexity_level') == 'high':
            return ExpertType.SENIOR_ADJUSTER
        
        # Default to claims reviewer
        return ExpertType.CLAIMS_REVIEWER
    
    def _map_claim_type(self, claim_type_str: Optional[str]) -> Optional[ClaimType]:
        """Map string claim type to enum."""
        if not claim_type_str:
            return None
        
        mapping = {
            'auto_liability': ClaimType.AUTO_LIABILITY,
            'property_damage': ClaimType.PROPERTY_DAMAGE,
            'bodily_injury': ClaimType.BODILY_INJURY,
            'general_liability': ClaimType.GENERAL_LIABILITY
        }
        
        return mapping.get(claim_type_str)
    
    async def _handle_overdue_interaction(self, interaction: Dict[str, Any]):
        """Handle overdue human interactions."""
        try:
            interaction_id = interaction['interaction_id']
            overdue_minutes = interaction.get('overdue_minutes', 0)
            
            logger.warning(f"Interaction {interaction_id} is overdue by {overdue_minutes} minutes")
            
            # Escalate if significantly overdue
            if overdue_minutes > 120:  # 2 hours
                await self.human_service.escalate_interaction(
                    interaction_id,
                    f"Interaction overdue by {overdue_minutes} minutes"
                )
                
        except Exception as e:
            logger.error(f"Error handling overdue interaction: {e}")
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status."""
        try:
            # Get database health
            from ..database import get_db_manager
            db_manager = get_db_manager()
            db_health = await db_manager.health_check()
            
            # Get cloud services health
            cloud_health = await self.cloud_services.get_health_status()
            
            # Get active interactions
            active_interactions = self.human_service.tools_manager.get_active_interactions()
            
            return {
                'status': 'running' if self.running else 'stopped',
                'database': db_health,
                'cloud_services': cloud_health,
                'active_interactions': len(active_interactions),
                'timestamp': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            }
