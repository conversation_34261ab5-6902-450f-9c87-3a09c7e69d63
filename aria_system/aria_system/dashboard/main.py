"""
Main Streamlit dashboard for ARIA system.
Real-time claims processing dashboard with live updates and analytics.
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import asyncio
from datetime import datetime, timedelta
import time
from typing import Dict, Any, List

# Configure Streamlit page
st.set_page_config(
    page_title="ARIA - Autonomous Risk Intelligence Agent",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
.main-header {
    background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
    padding: 1rem;
    border-radius: 10px;
    color: white;
    margin-bottom: 2rem;
    text-align: center;
}

.metric-card {
    background: white;
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #2a5298;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-processing {
    background-color: #fff3cd;
    color: #856404;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-review {
    background-color: #f8d7da;
    color: #721c24;
}

.human-interaction {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
}
</style>
""", unsafe_allow_html=True)


def main():
    """Main dashboard application."""
    
    # Header
    st.markdown("""
    <div class="main-header">
        <h1>🤖 ARIA - Autonomous Risk Intelligence Agent</h1>
        <p>Real-time Claims Processing Dashboard</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Sidebar navigation
    with st.sidebar:
        st.image("https://via.placeholder.com/200x80/2a5298/ffffff?text=ARIA", width=200)
        
        page = st.selectbox(
            "🧭 Navigate",
            [
                "📊 Real-Time Dashboard",
                "📧 Email Claims Processing", 
                "🤝 Human Expert Tools",
                "🤖 AI Agent Playground",
                "📈 Analytics & Insights",
                "🔍 Claim Tracker",
                "⚙️ System Status"
            ]
        )
        
        # Real-time stats in sidebar
        st.markdown("### 📊 Live Stats")
        col1, col2 = st.columns(2)
        with col1:
            st.metric("🔄 Active", "12")
            st.metric("✅ Today", "47")
        with col2:
            st.metric("👥 Human", "3")
            st.metric("⚡ Avg Time", "38m")
    
    # Route to appropriate page
    if page == "📊 Real-Time Dashboard":
        show_realtime_dashboard()
    elif page == "📧 Email Claims Processing":
        show_email_processing()
    elif page == "🤝 Human Expert Tools":
        show_human_tools()
    elif page == "🤖 AI Agent Playground":
        show_ai_playground()
    elif page == "📈 Analytics & Insights":
        show_analytics()
    elif page == "🔍 Claim Tracker":
        show_claim_tracker()
    elif page == "⚙️ System Status":
        show_system_status()


def show_realtime_dashboard():
    """Show real-time dashboard with live updates."""
    
    # Auto-refresh toggle
    auto_refresh = st.checkbox("🔄 Auto-refresh (30s)", value=True)

    if auto_refresh:
        # Auto-refresh every 30 seconds
        st.empty()  # Clear previous content
        time.sleep(1)  # Small delay for demo purposes
        st.rerun()
    
    # Key metrics row
    col1, col2, col3, col4, col5 = st.columns(5)
    
    # Generate real-time metrics
    import random
    current_time = datetime.now()

    # Dynamic metrics that change based on time
    claims_today = 35 + (current_time.hour * 2) + random.randint(-3, 5)
    avg_processing = 35 + random.randint(-5, 10)
    ai_accuracy = 94.5 + random.uniform(-1.5, 2.5)
    human_review_rate = 6.5 + random.uniform(-1.5, 3.0)
    claims_value = 2.1 + random.uniform(-0.3, 0.6)

    with col1:
        st.metric(
            label="📧 Claims Today",
            value=str(claims_today),
            delta=f"{random.randint(8, 15)} vs yesterday"
        )

    with col2:
        st.metric(
            label="⚡ Avg Processing",
            value=f"{avg_processing} min",
            delta=f"{random.randint(-25, -15)} min vs last week"
        )

    with col3:
        st.metric(
            label="🤖 AI Accuracy",
            value=f"{ai_accuracy:.1f}%",
            delta=f"{random.uniform(1.5, 3.5):.1f}% vs last month"
        )

    with col4:
        st.metric(
            label="👥 Human Reviews",
            value=f"{human_review_rate:.1f}%",
            delta=f"{random.uniform(-4.0, -2.0):.1f}% vs target"
        )

    with col5:
        st.metric(
            label="💰 Claims Value",
            value=f"${claims_value:.1f}M",
            delta=f"${random.uniform(0.3, 0.7):.1f}M vs last week"
        )
    
    # Real-time processing visualization
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📈 Real-Time Processing Flow")
        
        # Generate real-time data
        processing_data = generate_realtime_data()
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('Claims Volume', 'Processing Time'),
            vertical_spacing=0.1
        )
        
        # Claims volume
        fig.add_trace(
            go.Scatter(
                x=processing_data['timestamp'],
                y=processing_data['claims_processed'],
                mode='lines+markers',
                name='Claims Processed',
                line=dict(color='#2a5298', width=3)
            ),
            row=1, col=1
        )
        
        fig.add_trace(
            go.Scatter(
                x=processing_data['timestamp'],
                y=processing_data['human_reviews'],
                mode='lines+markers',
                name='Human Reviews',
                line=dict(color='#ff6b6b', width=2)
            ),
            row=1, col=1
        )
        
        # Processing time
        fig.add_trace(
            go.Scatter(
                x=processing_data['timestamp'],
                y=processing_data['avg_processing_time'],
                mode='lines+markers',
                name='Avg Processing Time (min)',
                line=dict(color='#4ecdc4', width=3)
            ),
            row=2, col=1
        )
        
        fig.update_layout(height=500, showlegend=True)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("🎯 Current Status")
        
        # Active claims status
        status_data = {
            'Status': ['🔄 Processing', '👥 Human Review', '✅ Completed', '⏳ Pending'],
            'Count': [12, 3, 47, 5]
        }
        
        fig_pie = px.pie(
            values=status_data['Count'],
            names=status_data['Status'],
            title="Claims by Status"
        )
        st.plotly_chart(fig_pie, use_container_width=True)
    
    # Recent claims table
    st.subheader("🔄 Recent Claims Activity")
    
    recent_claims = get_sample_claims_data()
    st.dataframe(recent_claims, use_container_width=True)


def show_email_processing():
    """Show email processing interface."""
    st.subheader("📧 Email Claims Processing")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        if st.button("🔄 Check for New Emails", type="primary"):
            with st.spinner("Checking for new emails..."):
                # Simulate email processing
                time.sleep(2)
                st.success("Found 3 new claims! Processing...")
                
                # Show processing steps
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                steps = [
                    "📧 Parsing emails...",
                    "📎 Extracting attachments...",
                    "🤖 Running AI analysis...",
                    "✅ Processing complete!"
                ]
                
                for i, step in enumerate(steps):
                    status_text.text(step)
                    progress_bar.progress((i + 1) / len(steps))
                    time.sleep(1)
    
    with col2:
        st.metric("📬 Unprocessed Emails", "7")
        st.metric("⏱️ Avg Response Time", "3.2 min")


def show_human_tools():
    """Show human expert tools interface."""
    st.subheader("🤝 Human Expert Tools - Live Interaction Center")
    
    # Tool selection and testing
    col1, col2 = st.columns([1, 2])
    
    with col1:
        st.markdown("### 🛠️ Available Expert Tools")
        
        expert_types = [
            "Claims Reviewer",
            "Senior Adjuster", 
            "Fraud Investigator",
            "Manager Approver",
            "Customer Service",
            "Legal Counsel"
        ]
        
        selected_expert = st.selectbox("Select Expert Type:", expert_types)
        
        st.markdown(f"""
        **Selected:** {selected_expert}
        
        **Expertise Areas:**
        • Claims analysis
        • Policy interpretation
        • Risk assessment
        
        **Expected Response:** 2-4 hours
        """)
    
    with col2:
        st.markdown("### 💬 Test Expert Interaction")
        
        with st.form("expert_test"):
            request_text = st.text_area(
                "Request for Expert:",
                placeholder="Enter your request for the expert...",
                height=100
            )
            
            urgency = st.selectbox("Urgency Level:", ["low", "normal", "high", "urgent"])
            
            submitted = st.form_submit_button("🚀 Contact Expert", type="primary")
            
            if submitted and request_text:
                with st.spinner(f"Contacting {selected_expert}..."):
                    time.sleep(2)
                    st.success("✅ Expert contacted successfully!")
                    st.info("⏳ Response expected within 2-4 hours")


def show_ai_playground():
    """Show AI agent playground for testing."""
    st.subheader("🤖 AI Agent Playground - Test ARIA Intelligence")
    
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown("### 📝 Claim Input")
        
        with st.form("ai_test_form"):
            claim_type = st.selectbox(
                "Claim Type:",
                ["Auto Liability", "Property Damage", "Bodily Injury", "General Liability"]
            )
            
            incident_description = st.text_area(
                "Incident Description:",
                placeholder="Describe the incident in detail...",
                height=150
            )
            
            claim_value = st.number_input(
                "Estimated Claim Value ($):",
                min_value=0,
                value=15000
            )
            
            submitted = st.form_submit_button("🚀 Analyze Claim", type="primary")
    
    with col2:
        st.markdown("### 🧠 AI Analysis Results")
        
        if submitted and incident_description:
            with st.spinner("🤖 ARIA is analyzing the claim..."):
                time.sleep(3)
                
                st.success("✅ Analysis Complete!")
                
                st.markdown(f"""
                **Coverage Decision:** 🟢 COVERED
                
                **Confidence Score:** 92%
                
                **Fault Assessment:** 75% at fault
                
                **Estimated Settlement:** ${claim_value * 0.8:,.0f}
                """)
                
                with st.expander("📋 Detailed Analysis"):
                    st.write("**Reasoning:** Based on the incident description, this appears to be a standard liability case with clear coverage under the policy.")


def show_analytics():
    """Show analytics and insights."""
    st.subheader("📈 Analytics & Insights Dashboard")
    
    # Key performance indicators
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("⚡ Processing Speed", "38 min avg", delta="-22 min vs target")
    
    with col2:
        st.metric("🎯 AI Accuracy", "96.8%", delta="+2.3% vs last period")
    
    with col3:
        st.metric("👥 Human Review Rate", "8.2%", delta="-3.1% vs target")
    
    with col4:
        st.metric("💰 Cost Savings", "$2.4M", delta="+$450K vs manual")
    
    # Charts
    col1, col2 = st.columns(2)
    
    with col1:
        # Processing time trends
        today = datetime.now().date()
        time_data = pd.DataFrame({
            'Date': pd.date_range(start=today - timedelta(days=29), periods=30, freq='D'),
            'Processing_Time': [45 - i*0.5 + (i%7)*2 for i in range(30)]
        })
        
        fig = px.line(time_data, x='Date', y='Processing_Time', title="Processing Time Trend")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Expert utilization
        expert_data = pd.DataFrame({
            'Expert': ['Claims Reviewer', 'Senior Adjuster', 'Fraud Investigator', 'Manager'],
            'Interactions': [45, 23, 8, 12]
        })
        
        fig = px.bar(expert_data, x='Expert', y='Interactions', title="Expert Utilization")
        st.plotly_chart(fig, use_container_width=True)


def show_claim_tracker():
    """Show individual claim tracking."""
    st.subheader("🔍 Claim Tracker")
    
    current_year = datetime.now().year
    claim_id = st.text_input("Enter Claim ID:", placeholder=f"CLAIM-{current_year}-001")
    
    if claim_id:
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Status", "Approved")
            st.metric("Claimant", "John Doe")
        
        with col2:
            st.metric("Type", "Auto Liability")
            st.metric("Value", "$15,000")
        
        with col3:
            st.metric("AI Confidence", "92%")
            st.metric("Processing Time", "28 minutes")
        
        # Timeline
        st.subheader("📅 Processing Timeline")
        
        timeline_events = [
            {"time": "14:30:00", "event": "📧 Claim received via email"},
            {"time": "14:30:15", "event": "📎 Documents extracted and validated"},
            {"time": "14:31:30", "event": "🤖 AI analysis completed"},
            {"time": "14:32:00", "event": "✅ Claim approved automatically"},
            {"time": "14:32:15", "event": "📧 Approval notification sent"}
        ]
        
        for event in timeline_events:
            st.write(f"✅ **{event['time']}** - {event['event']}")


def show_system_status():
    """Show system status and health."""
    st.subheader("⚙️ System Status")
    
    # System health
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Database", "✅ Healthy")
        st.metric("Redis", "✅ Healthy")
    
    with col2:
        st.metric("Email Service", "✅ Running")
        st.metric("AI Service", "✅ Running")
    
    with col3:
        st.metric("Cloud Services", "✅ Connected")
        st.metric("HumanLayer", "✅ Connected")
    
    # Recent logs
    st.subheader("📋 Recent Activity Logs")
    
    current_year = datetime.now().year
    current_time = datetime.now()
    logs = [
        {"time": (current_time - timedelta(minutes=2)).strftime("%H:%M:%S"), "level": "INFO", "message": f"Claim CLAIM-{current_year}-001 processed successfully"},
        {"time": (current_time - timedelta(minutes=3)).strftime("%H:%M:%S"), "level": "INFO", "message": "Email <NAME_EMAIL>"},
        {"time": (current_time - timedelta(minutes=5)).strftime("%H:%M:%S"), "level": "INFO", "message": "Human expert contacted for claim review"},
        {"time": (current_time - timedelta(minutes=7)).strftime("%H:%M:%S"), "level": "INFO", "message": "AI analysis completed with 94% confidence"}
    ]
    
    for log in logs:
        st.text(f"{log['time']} [{log['level']}] {log['message']}")


def generate_realtime_data() -> pd.DataFrame:
    """Generate real-time data for charts with dynamic values."""
    now = datetime.now()
    timestamps = [now - timedelta(minutes=i*5) for i in range(24, 0, -1)]

    # Generate more realistic real-time data with some randomness
    import random
    base_time = now.hour * 60 + now.minute  # Current time in minutes

    return pd.DataFrame({
        'timestamp': timestamps,
        'claims_processed': [max(1, 2 + i//3 + (i%4) + random.randint(-1, 2)) for i in range(24)],
        'human_reviews': [max(0, (i//8) + (i%3) - 1 + random.randint(-1, 1)) for i in range(24)],
        'avg_processing_time': [max(15, 45 - i*0.5 + (i%5)*3 + random.randint(-5, 5)) for i in range(24)]
    })


def get_sample_claims_data() -> pd.DataFrame:
    """Get sample claims data."""
    current_year = datetime.now().year
    return pd.DataFrame({
        'Claim ID': [f'CLAIM-{current_year}-047', f'CLAIM-{current_year}-046', f'CLAIM-{current_year}-045'],
        'Claimant': ['Sarah Johnson', 'Mike Chen', 'Lisa Rodriguez'],
        'Type': ['Auto Liability', 'Property Damage', 'Bodily Injury'],
        'Status': ['✅ Approved', '🔄 Processing', '👥 Human Review'],
        'Value': ['$15,000', '$8,500', '$45,000'],
        'Processing Time': ['28 min', '15 min', '1h 12min'],
        'Confidence': ['94%', '88%', '65%']
    })


def run():
    """Run the dashboard."""
    main()


if __name__ == "__main__":
    run()
