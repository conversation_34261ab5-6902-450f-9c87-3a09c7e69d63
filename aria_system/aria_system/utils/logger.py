"""
Centralized logging configuration for ARIA system.
Provides structured logging with different levels and output formats.
"""

import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from loguru import logger
from ..config import settings


class ARIALogger:
    """Centralized logger for ARIA system."""
    
    def __init__(self):
        self._configured = False
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging for the application."""
        if self._configured:
            return
            
        # Remove default logger
        logger.remove()
        
        # Console logging
        logger.add(
            sys.stdout,
            format=self._get_console_format(),
            level=settings.app.log_level,
            colorize=True,
            backtrace=True,
            diagnose=True,
        )
        
        # File logging
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Application logs
        logger.add(
            log_dir / "aria_{time:YYYY-MM-DD}.log",
            format=self._get_file_format(),
            level="INFO",
            rotation="1 day",
            retention="30 days",
            compression="zip",
            backtrace=True,
            diagnose=True,
        )
        
        # Error logs
        logger.add(
            log_dir / "aria_errors_{time:YYYY-MM-DD}.log",
            format=self._get_file_format(),
            level="ERROR",
            rotation="1 day",
            retention="90 days",
            compression="zip",
            backtrace=True,
            diagnose=True,
        )
        
        # Claims processing logs
        logger.add(
            log_dir / "claims_{time:YYYY-MM-DD}.log",
            format=self._get_file_format(),
            level="INFO",
            rotation="1 day",
            retention="365 days",  # Keep claims logs for a year
            compression="zip",
            filter=lambda record: "claims" in record["extra"],
        )
        
        # Human interaction logs
        logger.add(
            log_dir / "human_interactions_{time:YYYY-MM-DD}.log",
            format=self._get_file_format(),
            level="INFO",
            rotation="1 day",
            retention="365 days",
            compression="zip",
            filter=lambda record: "human_interaction" in record["extra"],
        )
        
        self._configured = True
        logger.info("ARIA logging system initialized")
    
    def _get_console_format(self) -> str:
        """Get console log format."""
        return (
            "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
            "<level>{level: <8}</level> | "
            "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
            "<level>{message}</level>"
        )
    
    def _get_file_format(self) -> str:
        """Get file log format."""
        return (
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | "
            "{name}:{function}:{line} | "
            "{extra} | "
            "{message}"
        )
    
    def get_logger(self, name: str) -> "logger":
        """Get a logger instance with the given name."""
        return logger.bind(name=name)
    
    def log_claim_event(self, claim_id: str, event: str, details: Optional[dict] = None):
        """Log a claim-related event."""
        logger.bind(
            claims=True,
            claim_id=claim_id,
            event=event,
            details=details or {}
        ).info(f"Claim {claim_id}: {event}")
    
    def log_human_interaction(self, interaction_id: str, expert_type: str, action: str, details: Optional[dict] = None):
        """Log a human interaction event."""
        logger.bind(
            human_interaction=True,
            interaction_id=interaction_id,
            expert_type=expert_type,
            action=action,
            details=details or {}
        ).info(f"Human interaction {interaction_id}: {action}")
    
    def log_ai_decision(self, claim_id: str, decision: str, confidence: float, details: Optional[dict] = None):
        """Log an AI decision event."""
        logger.bind(
            ai_decision=True,
            claim_id=claim_id,
            decision=decision,
            confidence=confidence,
            details=details or {}
        ).info(f"AI decision for claim {claim_id}: {decision} (confidence: {confidence:.2%})")
    
    def log_email_event(self, email_type: str, recipient: str, subject: str, success: bool):
        """Log an email event."""
        logger.bind(
            email=True,
            email_type=email_type,
            recipient=recipient,
            subject=subject,
            success=success
        ).info(f"Email {email_type} to {recipient}: {'sent' if success else 'failed'}")
    
    def log_document_processing(self, document_id: str, filename: str, provider: str, success: bool, details: Optional[dict] = None):
        """Log document processing event."""
        logger.bind(
            document_processing=True,
            document_id=document_id,
            filename=filename,
            provider=provider,
            success=success,
            details=details or {}
        ).info(f"Document {document_id} processed via {provider}: {'success' if success else 'failed'}")
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str, context: Optional[dict] = None):
        """Log a performance metric."""
        logger.bind(
            performance=True,
            metric_name=metric_name,
            value=value,
            unit=unit,
            context=context or {}
        ).info(f"Performance metric {metric_name}: {value} {unit}")


# Global logger instance
aria_logger = ARIALogger()


def get_logger(name: str = "aria") -> "logger":
    """Get a logger instance."""
    return aria_logger.get_logger(name)


def log_claim_event(claim_id: str, event: str, details: Optional[dict] = None):
    """Log a claim-related event."""
    aria_logger.log_claim_event(claim_id, event, details)


def log_human_interaction(interaction_id: str, expert_type: str, action: str, details: Optional[dict] = None):
    """Log a human interaction event."""
    aria_logger.log_human_interaction(interaction_id, expert_type, action, details)


def log_ai_decision(claim_id: str, decision: str, confidence: float, details: Optional[dict] = None):
    """Log an AI decision event."""
    aria_logger.log_ai_decision(claim_id, decision, confidence, details)


def log_email_event(email_type: str, recipient: str, subject: str, success: bool):
    """Log an email event."""
    aria_logger.log_email_event(email_type, recipient, subject, success)


def log_document_processing(document_id: str, filename: str, provider: str, success: bool, details: Optional[dict] = None):
    """Log document processing event."""
    aria_logger.log_document_processing(document_id, filename, provider, success, details)


def log_performance_metric(metric_name: str, value: float, unit: str, context: Optional[dict] = None):
    """Log a performance metric."""
    aria_logger.log_performance_metric(metric_name, value, unit, context)
