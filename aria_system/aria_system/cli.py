"""
Command-line interface for ARIA system.
Provides commands to start, stop, and manage the ARIA claims processing system.
"""

import asyncio
import sys
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

from .config import settings
from .database import init_database, close_database, get_db_manager
from .services.cloud_services import CloudServicesManager
from .services.email_service import EmailService
from .services.human_interaction_service import HumanInteractionService
from .utils.logger import get_logger

app = typer.Typer(
    name="aria",
    help="ARIA - Autonomous Risk Intelligence Agent CLI",
    add_completion=False
)
console = Console()
logger = get_logger(__name__)


@app.command()
def init(
    force: bool = typer.Option(False, "--force", "-f", help="Force initialization even if already initialized")
):
    """Initialize ARIA system database and configuration."""
    console.print(Panel.fit("🤖 ARIA System Initialization", style="bold blue"))
    
    async def _init():
        try:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console,
            ) as progress:
                
                # Initialize database
                task1 = progress.add_task("Initializing database...", total=None)
                await init_database()
                progress.update(task1, description="✅ Database initialized")
                
                # Test cloud services
                task2 = progress.add_task("Testing cloud services...", total=None)
                cloud_services = CloudServicesManager()
                health = await cloud_services.get_health_status()
                progress.update(task2, description="✅ Cloud services tested")
                
                # Test database connection
                task3 = progress.add_task("Testing database connection...", total=None)
                db_manager = get_db_manager()
                db_health = await db_manager.health_check()
                progress.update(task3, description="✅ Database connection tested")
            
            console.print("\n🎉 ARIA system initialized successfully!")
            
            # Display health status
            _display_health_status(health, db_health)
            
        except Exception as e:
            console.print(f"❌ Initialization failed: {e}", style="bold red")
            sys.exit(1)
    
    asyncio.run(_init())


@app.command()
def start(
    email_monitoring: bool = typer.Option(True, "--email/--no-email", help="Enable email monitoring"),
    dashboard: bool = typer.Option(True, "--dashboard/--no-dashboard", help="Start dashboard"),
    port: int = typer.Option(8000, "--port", "-p", help="API port"),
):
    """Start ARIA system services."""
    console.print(Panel.fit("🚀 Starting ARIA System", style="bold green"))
    
    async def _start():
        try:
            # Initialize services
            cloud_services = CloudServicesManager()
            email_service = EmailService(cloud_services)
            human_service = HumanInteractionService()
            
            console.print("📧 Email service initialized")
            console.print("🤝 Human interaction service initialized")
            console.print("☁️ Cloud services initialized")
            
            # Start email monitoring if enabled
            if email_monitoring:
                console.print("📬 Starting email monitoring...")
                email_task = asyncio.create_task(email_service.start_monitoring())
            
            # Start dashboard if enabled
            if dashboard:
                console.print(f"📊 Dashboard will be available at http://localhost:{settings.app.dashboard_port}")
            
            console.print("\n✅ ARIA system is running!")
            console.print("Press Ctrl+C to stop")
            
            # Keep running
            try:
                if email_monitoring:
                    await email_task
                else:
                    while True:
                        await asyncio.sleep(1)
            except KeyboardInterrupt:
                console.print("\n🛑 Stopping ARIA system...")
                if email_monitoring:
                    email_service.stop_monitoring()
                
        except Exception as e:
            console.print(f"❌ Failed to start system: {e}", style="bold red")
            sys.exit(1)
    
    asyncio.run(_start())


@app.command()
def status():
    """Show ARIA system status."""
    console.print(Panel.fit("📊 ARIA System Status", style="bold cyan"))
    
    async def _status():
        try:
            # Check database
            db_manager = get_db_manager()
            db_health = await db_manager.health_check()
            
            # Check cloud services
            cloud_services = CloudServicesManager()
            cloud_health = await cloud_services.get_health_status()
            
            # Check human interaction service
            human_service = HumanInteractionService()
            active_interactions = human_service.tools_manager.get_active_interactions()
            overdue_interactions = await human_service.check_overdue_interactions()
            
            _display_detailed_status(db_health, cloud_health, active_interactions, overdue_interactions)
            
        except Exception as e:
            console.print(f"❌ Failed to get status: {e}", style="bold red")
    
    asyncio.run(_status())


@app.command()
def test_email():
    """Test email processing functionality."""
    console.print(Panel.fit("📧 Testing Email Processing", style="bold yellow"))
    
    async def _test_email():
        try:
            cloud_services = CloudServicesManager()
            email_service = EmailService(cloud_services)
            
            console.print("🔍 Checking for new emails...")
            emails = await email_service.check_new_emails()
            
            if emails:
                console.print(f"📬 Found {len(emails)} new emails")
                for email_data in emails:
                    console.print(f"  • {email_data['claim_number']} from {email_data['sender_email']}")
            else:
                console.print("📭 No new emails found")
                
        except Exception as e:
            console.print(f"❌ Email test failed: {e}", style="bold red")
    
    asyncio.run(_test_email())


@app.command()
def test_human_tools():
    """Test human interaction tools."""
    console.print(Panel.fit("🤝 Testing Human Tools", style="bold magenta"))
    
    async def _test_human_tools():
        try:
            human_service = HumanInteractionService()
            
            # Test claims reviewer
            console.print("🧑‍💼 Testing Claims Reviewer...")
            result = await human_service.contact_expert(
                expert_type="claims_reviewer",
                request="Test request for claims review",
                context={"claim_id": "TEST-001", "amount": 25000},
                urgency="normal"
            )
            
            console.print(f"✅ Response received: {result['interaction_id']}")
            console.print(f"📝 Status: {result['status']}")
            
        except Exception as e:
            console.print(f"❌ Human tools test failed: {e}", style="bold red")
    
    asyncio.run(_test_human_tools())


@app.command()
def dashboard():
    """Start the Streamlit dashboard."""
    console.print(Panel.fit("📊 Starting ARIA Dashboard", style="bold blue"))
    
    try:
        import subprocess
        import os
        
        # Change to the dashboard directory
        dashboard_path = Path(__file__).parent / "dashboard"
        
        # Start Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            str(dashboard_path / "main.py"),
            "--server.port", str(settings.app.dashboard_port),
            "--server.address", settings.app.dashboard_host
        ]
        
        console.print(f"🌐 Dashboard starting at http://{settings.app.dashboard_host}:{settings.app.dashboard_port}")
        subprocess.run(cmd)
        
    except Exception as e:
        console.print(f"❌ Failed to start dashboard: {e}", style="bold red")


@app.command()
def config():
    """Show current configuration."""
    console.print(Panel.fit("⚙️ ARIA Configuration", style="bold white"))
    
    table = Table(title="Configuration Settings")
    table.add_column("Setting", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Environment", settings.app.environment)
    table.add_row("Debug Mode", str(settings.app.debug))
    table.add_row("Log Level", settings.app.log_level)
    table.add_row("Database URL", settings.get_database_url()[:50] + "...")
    table.add_row("Redis URL", settings.get_redis_url())
    table.add_row("API Host", f"{settings.app.api_host}:{settings.app.api_port}")
    table.add_row("Dashboard Host", f"{settings.app.dashboard_host}:{settings.app.dashboard_port}")
    
    # Cloud providers
    providers = settings.get_enabled_cloud_providers()
    table.add_row("Cloud Providers", ", ".join(providers) if providers else "None")
    
    console.print(table)


def _display_health_status(cloud_health: dict, db_health: dict):
    """Display health status in a formatted table."""
    table = Table(title="System Health Status")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details")
    
    # Database status
    db_status = "✅ Healthy" if db_health["database"] else "❌ Unhealthy"
    table.add_row("Database", db_status, db_health["details"].get("database", ""))
    
    redis_status = "✅ Healthy" if db_health["redis"] else "❌ Unhealthy"
    table.add_row("Redis", redis_status, db_health["details"].get("redis", ""))
    
    # Cloud services status
    for provider, status in cloud_health.items():
        if status["enabled"]:
            provider_status = "✅ Enabled" if status["services"] else "⚠️ Enabled (No services tested)"
            table.add_row(f"Cloud ({provider.upper()})", provider_status, str(status["services"]))
    
    console.print(table)


def _display_detailed_status(db_health: dict, cloud_health: dict, active_interactions: dict, overdue_interactions: list):
    """Display detailed system status."""
    # System health
    _display_health_status(cloud_health, db_health)
    
    # Human interactions
    console.print("\n🤝 Human Interactions")
    if active_interactions:
        interaction_table = Table(title="Active Interactions")
        interaction_table.add_column("ID", style="cyan")
        interaction_table.add_column("Expert", style="green")
        interaction_table.add_column("Status", style="yellow")
        interaction_table.add_column("Duration")
        
        for interaction_id, data in active_interactions.items():
            duration = f"{data.get('response_time_minutes', 0)} min"
            interaction_table.add_row(
                interaction_id[:12] + "...",
                data["expert_type"],
                data["status"],
                duration
            )
        
        console.print(interaction_table)
    else:
        console.print("No active interactions")
    
    # Overdue interactions
    if overdue_interactions:
        console.print(f"\n⚠️ {len(overdue_interactions)} overdue interactions")


def main():
    """Main CLI entry point."""
    app()


if __name__ == "__main__":
    main()
