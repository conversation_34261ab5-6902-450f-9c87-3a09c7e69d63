"""
Database connection and session management for ARIA system.
"""

import asyncio
from contextlib import contextmanager, asynccontextmanager
from typing import Generator, AsyncGenerator

from sqlalchemy import create_engine, event
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import redis.asyncio as redis

from .config import settings
from .models.base import Base
from .utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """Database connection and session manager."""
    
    def __init__(self):
        self.engine = None
        self.async_engine = None
        self.SessionLocal = None
        self.AsyncSessionLocal = None
        self.redis_client = None
        self._initialized = False
    
    def initialize(self):
        """Initialize database connections."""
        if self._initialized:
            return
        
        # Create synchronous engine
        self.engine = create_engine(
            settings.get_database_url(),
            pool_size=settings.database.pool_size,
            max_overflow=settings.database.max_overflow,
            pool_timeout=settings.database.pool_timeout,
            echo=settings.is_development(),
        )
        
        # Create async engine
        async_url = settings.get_database_url().replace("postgresql://", "postgresql+asyncpg://")
        self.async_engine = create_async_engine(
            async_url,
            pool_size=settings.database.pool_size,
            max_overflow=settings.database.max_overflow,
            echo=settings.is_development(),
        )
        
        # Create session factories
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        self.AsyncSessionLocal = async_sessionmaker(
            self.async_engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Initialize Redis
        self.redis_client = redis.from_url(
            settings.get_redis_url(),
            encoding="utf-8",
            decode_responses=True
        )
        
        # Add event listeners
        self._add_event_listeners()
        
        self._initialized = True
        logger.info("Database manager initialized")
    
    def _add_event_listeners(self):
        """Add database event listeners for logging and monitoring."""
        
        @event.listens_for(self.engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """Set SQLite pragmas for better performance."""
            if "sqlite" in str(self.engine.url):
                cursor = dbapi_connection.cursor()
                cursor.execute("PRAGMA foreign_keys=ON")
                cursor.close()
        
        @event.listens_for(self.engine, "before_cursor_execute")
        def receive_before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Log slow queries in development."""
            if settings.is_development():
                context._query_start_time = asyncio.get_event_loop().time()
        
        @event.listens_for(self.engine, "after_cursor_execute")
        def receive_after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            """Log slow queries in development."""
            if settings.is_development():
                total = asyncio.get_event_loop().time() - context._query_start_time
                if total > 0.1:  # Log queries taking more than 100ms
                    logger.warning(f"Slow query detected: {total:.3f}s - {statement[:100]}...")
    
    async def create_tables(self):
        """Create all database tables."""
        if not self._initialized:
            self.initialize()
        
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created")
    
    async def drop_tables(self):
        """Drop all database tables."""
        if not self._initialized:
            self.initialize()
        
        async with self.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.info("Database tables dropped")
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get a synchronous database session."""
        if not self._initialized:
            self.initialize()
        
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get an asynchronous database session."""
        if not self._initialized:
            self.initialize()
        
        async with self.AsyncSessionLocal() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    async def get_redis(self) -> redis.Redis:
        """Get Redis client."""
        if not self._initialized:
            self.initialize()
        
        return self.redis_client
    
    async def health_check(self) -> dict:
        """Perform database health check."""
        health = {
            "database": False,
            "redis": False,
            "details": {}
        }
        
        # Check PostgreSQL
        try:
            async with self.get_async_session() as session:
                result = await session.execute("SELECT 1")
                if result.scalar() == 1:
                    health["database"] = True
                    health["details"]["database"] = "Connected"
        except Exception as e:
            health["details"]["database"] = f"Error: {str(e)}"
        
        # Check Redis
        try:
            redis_client = await self.get_redis()
            await redis_client.ping()
            health["redis"] = True
            health["details"]["redis"] = "Connected"
        except Exception as e:
            health["details"]["redis"] = f"Error: {str(e)}"
        
        return health
    
    async def close(self):
        """Close all database connections."""
        if self.async_engine:
            await self.async_engine.dispose()
        
        if self.engine:
            self.engine.dispose()
        
        if self.redis_client:
            await self.redis_client.close()
        
        logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


def get_db_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    return db_manager


@contextmanager
def get_db() -> Generator[Session, None, None]:
    """Get a database session (synchronous)."""
    with db_manager.get_session() as session:
        yield session


@asynccontextmanager
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Get a database session (asynchronous)."""
    async with db_manager.get_async_session() as session:
        yield session


async def get_redis() -> redis.Redis:
    """Get Redis client."""
    return await db_manager.get_redis()


async def init_database():
    """Initialize database and create tables."""
    db_manager.initialize()
    await db_manager.create_tables()
    logger.info("Database initialized successfully")


async def close_database():
    """Close database connections."""
    await db_manager.close()


# Dependency for FastAPI
async def get_db_dependency() -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency for database sessions."""
    async with get_async_db() as session:
        yield session


async def get_redis_dependency() -> redis.Redis:
    """FastAPI dependency for Redis client."""
    return await get_redis()
