#!/usr/bin/env python3
"""
ARIA Demo Validation Script
Validates that all components are ready for demonstration.
"""

import os
import sys
import importlib
from pathlib import Path
from typing import List, Dict, Any


class DemoValidator:
    """Validates ARIA demo readiness."""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.checks_passed = 0
        self.total_checks = 0
    
    def check(self, condition: bool, success_msg: str, error_msg: str, warning: bool = False):
        """Perform a validation check."""
        self.total_checks += 1
        
        if condition:
            self.checks_passed += 1
            print(f"✅ {success_msg}")
        else:
            if warning:
                self.warnings.append(error_msg)
                print(f"⚠️  {error_msg}")
            else:
                self.errors.append(error_msg)
                print(f"❌ {error_msg}")
    
    def validate_file_structure(self):
        """Validate project file structure."""
        print("\n📁 Validating File Structure")
        print("-" * 40)
        
        required_files = [
            "aria_system/__init__.py",
            "aria_system/config.py",
            "aria_system/database.py",
            "aria_system/cli.py",
            "aria_system/models/__init__.py",
            "aria_system/models/claims.py",
            "aria_system/models/documents.py",
            "aria_system/models/human_interactions.py",
            "aria_system/models/audit_logs.py",
            "aria_system/services/__init__.py",
            "aria_system/services/cloud_services.py",
            "aria_system/services/email_service.py",
            "aria_system/services/human_interaction_service.py",
            "aria_system/services/ai_agent_service.py",
            "aria_system/services/document_service.py",
            "aria_system/core/__init__.py",
            "aria_system/core/application.py",
            "aria_system/dashboard/__init__.py",
            "aria_system/dashboard/main.py",
            "aria_system/utils/__init__.py",
            "aria_system/utils/logger.py",
            "requirements.txt",
            "pyproject.toml",
            ".env.example",
            "README.md",
            "run_demo.py"
        ]
        
        for file_path in required_files:
            exists = os.path.exists(file_path)
            self.check(
                exists,
                f"Found {file_path}",
                f"Missing required file: {file_path}"
            )
    
    def validate_python_imports(self):
        """Validate that all Python modules can be imported."""
        print("\n🐍 Validating Python Imports")
        print("-" * 40)
        
        modules_to_test = [
            "aria_system",
            "aria_system.config",
            "aria_system.database",
            "aria_system.models.claims",
            "aria_system.models.documents",
            "aria_system.services.cloud_services",
            "aria_system.services.email_service",
            "aria_system.services.human_interaction_service",
            "aria_system.services.ai_agent_service",
            "aria_system.core.application",
            "aria_system.dashboard.main"
        ]
        
        for module_name in modules_to_test:
            try:
                importlib.import_module(module_name)
                self.check(
                    True,
                    f"Successfully imported {module_name}",
                    f"Failed to import {module_name}"
                )
            except ImportError as e:
                self.check(
                    False,
                    f"Successfully imported {module_name}",
                    f"Failed to import {module_name}: {str(e)}"
                )
    
    def validate_dependencies(self):
        """Validate required dependencies."""
        print("\n📦 Validating Dependencies")
        print("-" * 40)
        
        required_packages = [
            "fastapi",
            "uvicorn",
            "pydantic",
            "sqlalchemy",
            "alembic",
            "redis",
            "openai",
            "langchain",
            "langchain-openai",
            "humanlayer",
            "streamlit",
            "plotly",
            "pandas",
            "loguru",
            "typer",
            "rich"
        ]
        
        for package in required_packages:
            try:
                importlib.import_module(package.replace("-", "_"))
                self.check(
                    True,
                    f"Found {package}",
                    f"Missing package: {package}"
                )
            except ImportError:
                self.check(
                    False,
                    f"Found {package}",
                    f"Missing package: {package}",
                    warning=True
                )
    
    def validate_configuration(self):
        """Validate configuration files."""
        print("\n⚙️ Validating Configuration")
        print("-" * 40)
        
        # Check .env.example exists
        env_example_exists = os.path.exists(".env.example")
        self.check(
            env_example_exists,
            "Found .env.example file",
            "Missing .env.example file"
        )
        
        # Check if .env exists
        env_exists = os.path.exists(".env")
        self.check(
            env_exists,
            "Found .env file",
            "Missing .env file - copy .env.example to .env and configure",
            warning=True
        )
        
        # Validate requirements.txt
        req_exists = os.path.exists("requirements.txt")
        self.check(
            req_exists,
            "Found requirements.txt",
            "Missing requirements.txt"
        )
        
        if req_exists:
            with open("requirements.txt", "r") as f:
                content = f.read()
                essential_deps = ["fastapi", "streamlit", "openai", "langchain", "humanlayer"]
                for dep in essential_deps:
                    has_dep = dep in content
                    self.check(
                        has_dep,
                        f"Requirements includes {dep}",
                        f"Requirements missing {dep}",
                        warning=True
                    )
    
    def validate_demo_components(self):
        """Validate demo-specific components."""
        print("\n🎬 Validating Demo Components")
        print("-" * 40)
        
        # Check demo runner
        demo_runner_exists = os.path.exists("run_demo.py")
        self.check(
            demo_runner_exists,
            "Found demo runner script",
            "Missing run_demo.py"
        )
        
        # Check test scenarios
        test_scenarios_exist = os.path.exists("tests/test_demo_scenarios.py")
        self.check(
            test_scenarios_exist,
            "Found demo scenarios",
            "Missing demo scenarios"
        )
        
        # Check dashboard
        dashboard_exists = os.path.exists("aria_system/dashboard/main.py")
        self.check(
            dashboard_exists,
            "Found dashboard",
            "Missing dashboard"
        )
        
        # Check CLI
        cli_exists = os.path.exists("aria_system/cli.py")
        self.check(
            cli_exists,
            "Found CLI",
            "Missing CLI"
        )
    
    def validate_demo_scenarios(self):
        """Validate demo scenarios are complete."""
        print("\n📋 Validating Demo Scenarios")
        print("-" * 40)
        
        try:
            from tests.test_demo_scenarios import DemoScenarios
            
            scenarios = DemoScenarios.get_all_demo_scenarios()
            
            self.check(
                len(scenarios) >= 4,
                f"Found {len(scenarios)} demo scenarios",
                "Insufficient demo scenarios (need at least 4)"
            )
            
            # Check scenario variety
            claim_types = set()
            amounts = []
            
            for scenario in scenarios:
                if 'claim_info' in scenario:
                    claim_types.add(scenario['claim_info'].get('claim_type'))
                    amounts.append(scenario['claim_info'].get('estimated_amount', 0))
            
            self.check(
                len(claim_types) >= 3,
                f"Found {len(claim_types)} different claim types",
                "Need more variety in claim types"
            )
            
            self.check(
                max(amounts) > 50000,
                "Found high-value claims for manager approval demo",
                "Missing high-value claims for demo"
            )
            
            self.check(
                min(amounts) < 20000,
                "Found low-value claims for auto-approval demo",
                "Missing low-value claims for demo"
            )
            
        except ImportError as e:
            self.check(
                False,
                "Demo scenarios module imported successfully",
                f"Failed to import demo scenarios: {str(e)}"
            )
    
    def validate_readme_and_docs(self):
        """Validate documentation."""
        print("\n📚 Validating Documentation")
        print("-" * 40)
        
        readme_exists = os.path.exists("README.md")
        self.check(
            readme_exists,
            "Found README.md",
            "Missing README.md"
        )
        
        if readme_exists:
            with open("README.md", "r") as f:
                content = f.read()
                
                required_sections = [
                    "# 🤖 ARIA",
                    "## 🌟 Key Features",
                    "## 🚀 Quick Start",
                    "## ⚙️ Configuration",
                    "## 📧 Email Processing Flow",
                    "## 🤝 Human Expert Integration",
                    "## 📊 Dashboard Features"
                ]
                
                for section in required_sections:
                    has_section = section in content
                    self.check(
                        has_section,
                        f"README includes {section}",
                        f"README missing {section}",
                        warning=True
                    )
    
    def run_validation(self) -> bool:
        """Run complete validation."""
        print("🔍 ARIA Demo Validation")
        print("=" * 50)
        
        self.validate_file_structure()
        self.validate_python_imports()
        self.validate_dependencies()
        self.validate_configuration()
        self.validate_demo_components()
        self.validate_demo_scenarios()
        self.validate_readme_and_docs()
        
        # Summary
        print("\n📊 Validation Summary")
        print("=" * 50)
        
        success_rate = (self.checks_passed / self.total_checks) * 100
        
        print(f"✅ Checks Passed: {self.checks_passed}/{self.total_checks} ({success_rate:.1f}%)")
        
        if self.warnings:
            print(f"⚠️  Warnings: {len(self.warnings)}")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        if self.errors:
            print(f"❌ Errors: {len(self.errors)}")
            for error in self.errors:
                print(f"   • {error}")
        
        # Determine readiness
        critical_errors = [e for e in self.errors if "Missing required file" in e or "Failed to import aria_system" in e]
        
        if not critical_errors and success_rate >= 80:
            print("\n🎉 ARIA Demo is READY!")
            print("🚀 Run 'python run_demo.py' to start")
            return True
        elif not critical_errors:
            print("\n⚠️  ARIA Demo is MOSTLY READY")
            print("🔧 Address warnings for best demo experience")
            print("🚀 Run 'python run_demo.py' to start")
            return True
        else:
            print("\n❌ ARIA Demo is NOT READY")
            print("🔧 Please fix critical errors before demo")
            return False


def main():
    """Main validation function."""
    validator = DemoValidator()
    ready = validator.run_validation()
    
    if ready:
        print("\n🎯 Demo Tips:")
        print("1. Start with the Real-Time Dashboard")
        print("2. Show email processing simulation")
        print("3. Demonstrate human expert tools")
        print("4. Highlight AI analysis capabilities")
        print("5. Review analytics and performance metrics")
    
    return 0 if ready else 1


if __name__ == "__main__":
    sys.exit(main())
