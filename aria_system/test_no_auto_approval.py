"""
Test script to verify NO auto-approval functionality
"""

from aria_system.services.ai_analysis import AIAnalysisService
from aria_system.config import Settings

def test_no_auto_approval():
    print('🔒 Testing NO AUTO-APPROVAL Implementation')
    print('=' * 50)

    settings = Settings()
    ai_service = AIAnalysisService(settings)

    # Test with perfect AI analysis (high confidence, low fraud risk)
    perfect_analysis = {
        'classification': {
            'confidence_score': 0.99,  # 99% confidence
            'severity': 'low',
            'estimated_amount': 1000   # Low amount
        },
        'fraud_analysis': {
            'fraud_risk_score': 0.01  # 1% fraud risk
        }
    }

    # This should STILL require human review
    requires_human = ai_service._requires_human_review(perfect_analysis)

    print(f'AI Analysis Results:')
    print(f'  Confidence: {perfect_analysis["classification"]["confidence_score"]*100}%')
    print(f'  Fraud Risk: {perfect_analysis["fraud_analysis"]["fraud_risk_score"]*100}%')
    print(f'  Amount: ${perfect_analysis["classification"]["estimated_amount"]}')
    print(f'  Severity: {perfect_analysis["classification"]["severity"]}')

    print(f'\nHuman Review Required: {"✅ YES" if requires_human else "❌ NO (DANGEROUS!)"}')

    if requires_human:
        print('\n🎉 SUCCESS: Even perfect AI analysis requires human review!')
        print('✅ No auto-approval functionality detected')
        print('✅ All claims will be reviewed by human agents')
    else:
        print('\n❌ DANGER: Auto-approval is still possible!')
        print('❌ This needs to be fixed immediately!')

    print('\nConfiguration Check:')
    print(f'  Auto-approval enabled: {getattr(settings.app, "enable_auto_approval", "Not set")}')
    print(f'  Human review threshold: {getattr(settings.app, "human_review_threshold", "Not set")}')

if __name__ == "__main__":
    test_no_auto_approval()
